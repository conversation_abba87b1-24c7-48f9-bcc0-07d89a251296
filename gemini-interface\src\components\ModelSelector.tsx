'use client';

import { useState, useCallback } from 'react';
import { ChevronDown, Check, Loader2 } from 'lucide-react';
import { useChatContext } from '@/contexts/ChatContext';

export default function ModelSelector() {
  const { 
    currentModel, 
    availableModels, 
    setCurrentModel, 
    isModelLoading 
  } = useChatContext();
  
  const [isOpen, setIsOpen] = useState(false);
  
  const handleSelectModel = useCallback(async (modelId: string) => {
    if (modelId !== currentModel.id) {
      await setCurrentModel(modelId);
      setIsOpen(false);
    }
  }, [currentModel.id, setCurrentModel]);

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        disabled={isModelLoading}
        className={`flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-lg border ${
          isModelLoading ? 'bg-gray-100' : 'bg-white hover:bg-gray-50'
        } transition-colors`}
        aria-label="Select model"
      >
        {isModelLoading ? (
          <>
            <Loader2 className="w-4 h-4 animate-spin" />
            <span>Switching model...</span>
          </>
        ) : (
          <>
            <span className="text-gray-700">{currentModel.name}</span>
            <ChevronDown className="w-4 h-4 text-gray-500" />
          </>
        )}
      </button>
      
      {isOpen && (
        <>
          {/* Click outside to close */}
          <div 
            className="fixed inset-0 z-40"
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown */}
          <div className="absolute right-0 z-50 w-64 mt-1 origin-top-right bg-white rounded-lg shadow-lg ring-1 ring-black ring-opacity-5">
            <div className="py-1" role="menu" aria-orientation="vertical">
              <div className="px-4 py-2 text-xs font-medium text-gray-500 uppercase tracking-wider">
                Available Models
              </div>
              
              {availableModels.map((model) => (
                <button
                  key={model.id}
                  onClick={() => handleSelectModel(model.id)}
                  className={`flex items-center justify-between w-full px-4 py-2 text-sm text-left ${
                    model.id === currentModel.id 
                      ? 'bg-blue-50 text-blue-700' 
                      : 'text-gray-700 hover:bg-gray-50'
                  }`}
                  role="menuitem"
                >
                  <div className="flex-1">
                    <div className="font-medium">{model.name}</div>
                    <div className="text-xs text-gray-500">
                      {model.description}
                    </div>
                    <div className="text-xs text-gray-400 mt-1">
                      Max tokens: {model.maxTokens.toLocaleString()}
                    </div>
                  </div>
                  {model.id === currentModel.id && (
                    <Check className="w-4 h-4 text-blue-500" />
                  )}
                </button>
              ))}
            </div>
          </div>
        </>
      )}
    </div>
  );
}
