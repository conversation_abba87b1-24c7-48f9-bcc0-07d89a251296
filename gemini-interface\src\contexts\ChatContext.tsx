'use client';

import { create<PERSON>ontext, useContext, ReactNode, useState, useCallback, useMemo, useEffect } from 'react';
import { ModelConfig, DEFAULT_MODEL, AVAILABLE_MODELS } from '@/config/models';
import { geminiService, onboardingService } from '@/lib/api';

type Message = {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  includeInContext: boolean;
};

type SystemPrompt = {
  id: string;
  name: string;
  content: string;
  isDefault: boolean;
};

type ChatContextType = {
  // Messages
  messages: Message[];
  addMessage: (message: Omit<Message, 'id' | 'timestamp' | 'includeInContext'>) => void;
  updateMessage: (id: string, updates: Partial<Message>) => void;
  clearMessages: () => void;
  
  // Token management
  tokenCount: number;
  maxTokens: number;
  setMaxTokens: (tokens: number) => void;
  
  // System prompts
  systemPrompts: SystemPrompt[];
  activePromptId: string | null;
  addSystemPrompt: (prompt: Omit<SystemPrompt, 'id'>) => void;
  updateSystemPrompt: (id: string, updates: Partial<SystemPrompt>) => void;
  deleteSystemPrompt: (id: string) => void;
  setActivePrompt: (id: string | null) => void;
  getActivePrompt: () => SystemPrompt | null;
  
  // Model management
  availableModels: ModelConfig[];
  currentModel: ModelConfig;
  setCurrentModel: (modelId: string) => void;
  isModelLoading: boolean;
};

const ChatContext = createContext<ChatContextType | undefined>(undefined);

export function ChatProvider({ children }: { children: ReactNode }) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [maxTokens, setMaxTokens] = useState(DEFAULT_MODEL.maxTokens);
  const [currentModel, setCurrentModelState] = useState<ModelConfig>(DEFAULT_MODEL);
  const [isModelLoading, setIsModelLoading] = useState(false);
  const [systemPrompts, setSystemPrompts] = useState<SystemPrompt[]>(() => {
    // Initialize with a default prompt
    const defaultPrompt: SystemPrompt = {
      id: 'default-prompt',
      name: 'Default Assistant',
      content: 'You are a helpful AI assistant. Answer questions helpfully and concisely.',
      isDefault: true,
    };
    return [defaultPrompt];
  });
  const [activePromptId, setActivePromptId] = useState<string | null>('default-prompt');
  
  // Calculate token count (simplified)
  const tokenCount = useMemo(() => {
    const activePrompt = systemPrompts.find(p => p.id === activePromptId);
    const promptTokens = activePrompt ? Math.ceil(activePrompt.content.length / 4) : 0;
    
    const messageTokens = messages
      .filter(m => m.includeInContext)
      .reduce((count, msg) => count + Math.ceil(msg.content.length / 4), 0);
      
    return promptTokens + messageTokens;
  }, [messages, systemPrompts, activePromptId]);

  const addMessage = useCallback(async (message: Omit<Message, 'id' | 'timestamp' | 'includeInContext'>) => {
    const newMessage: Message = {
      ...message,
      id: `msg-${Date.now()}`,
      timestamp: new Date(),
      includeInContext: true,
    };
    
    setMessages(prev => [...prev, newMessage]);
    
    // If it's a user message, get AI response
    if (message.role === 'user') {
      try {
        // First get intent
        const intentResponse = await onboardingService.getIntent(message.content);
        
        // Then get AI response
        const aiResponse = await geminiService.chat(message.content);
        
        // Add AI response to messages
        setMessages(prev => [
          ...prev,
          {
            id: `msg-${Date.now()}`,
            role: 'assistant',
            content: aiResponse.message || aiResponse.content || 'I received your message.',
            timestamp: new Date(),
            includeInContext: true,
          },
        ]);
      } catch (error) {
        console.error('Error getting AI response:', error);
        setMessages(prev => [
          ...prev,
          {
            id: `msg-${Date.now()}`,
            role: 'assistant',
            content: 'Sorry, I encountered an error processing your request.',
            timestamp: new Date(),
            includeInContext: false,
          },
        ]);
      }
    }
  }, []);

  const updateMessage = useCallback((id: string, updates: Partial<Message>) => {
    setMessages(prev => 
      prev.map(msg => 
        msg.id === id ? { ...msg, ...updates } : msg
      )
    );
  }, []);

  const clearMessages = useCallback(() => {
    setMessages([]);
  }, []);
  
  // System prompt methods
  const addSystemPrompt = useCallback((prompt: Omit<SystemPrompt, 'id'>) => {
    const newPrompt: SystemPrompt = {
      ...prompt,
      id: `prompt-${Date.now()}`,
    };
    
    setSystemPrompts(prev => {
      // If setting as default, unset other defaults
      if (prompt.isDefault) {
        return prev.map(p => ({
          ...p,
          isDefault: false,
        })).concat(newPrompt);
      }
      return [...prev, newPrompt];
    });
    
    setActivePromptId(newPrompt.id);
  }, []);
  
  const updateSystemPrompt = useCallback((id: string, updates: Partial<SystemPrompt>) => {
    setSystemPrompts(prev => {
      // If setting as default, unset other defaults
      if (updates.isDefault) {
        return prev.map(p => ({
          ...p,
          isDefault: p.id === id,
          ...(p.id === id ? updates : {}),
        }));
      }
      return prev.map(p => (p.id === id ? { ...p, ...updates } : p));
    });
  }, []);
  
  const deleteSystemPrompt = useCallback((id: string) => {
    setSystemPrompts(prev => {
      const newPrompts = prev.filter(p => p.id !== id);
      // If we deleted the active prompt, set the first available prompt as active
      if (id === activePromptId) {
        setActivePromptId(newPrompts[0]?.id || null);
      }
      return newPrompts;
    });
  }, [activePromptId]);
  
  const setActivePrompt = useCallback((id: string | null) => {
    setActivePromptId(id);
  }, []);
  
  const getActivePrompt = useCallback((): SystemPrompt | null => {
    return systemPrompts.find(p => p.id === activePromptId) || null;
  }, [systemPrompts, activePromptId]);

  const setCurrentModel = useCallback(async (modelId: string) => {
    const model = AVAILABLE_MODELS.find(m => m.id === modelId) || DEFAULT_MODEL;
    if (model.id !== currentModel.id) {
      setIsModelLoading(true);
      // In a real app, you might want to save the current conversation
      // or warn the user before switching models
      try {
        // Simulate API call to switch models
        await new Promise(resolve => setTimeout(resolve, 500));
        setCurrentModelState(model);
        setMaxTokens(model.maxTokens);
      } catch (error) {
        console.error('Failed to switch model:', error);
      } finally {
        setIsModelLoading(false);
      }
    }
  }, [currentModel.id]);

  const contextValue = useMemo(() => ({
    // Message methods
    messages,
    addMessage,
    updateMessage,
    clearMessages,
    
    // Token management
    tokenCount,
    maxTokens,
    setMaxTokens,
    
    // System prompt methods
    systemPrompts,
    activePromptId,
    addSystemPrompt,
    updateSystemPrompt,
    deleteSystemPrompt,
    setActivePrompt,
    getActivePrompt,
    
    // Model management
    availableModels: AVAILABLE_MODELS,
    currentModel,
    setCurrentModel,
    isModelLoading,
  }), [
    messages,
    addMessage,
    updateMessage,
    clearMessages,
    tokenCount,
    maxTokens,
    systemPrompts,
    activePromptId,
    addSystemPrompt,
    updateSystemPrompt,
    deleteSystemPrompt,
    setActivePrompt,
    getActivePrompt,
    currentModel,
    isModelLoading,
    setCurrentModel,
  ]);

  return (
    <ChatContext.Provider value={contextValue}>
      {children}
    </ChatContext.Provider>
  );
}

export function useChatContext() {
  const context = useContext(ChatContext);
  if (context === undefined) {
    throw new Error('useChatContext must be used within a ChatProvider');
  }
  return context;
}
