INFO:     Will watch for changes in these directories: ['D:\\Projects\\myvillage\\Git_Repo\\myvillageai\\myvillageai\\api']
INFO:     Uvicorn running on http://127.0.0.1:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [4012] using WatchFiles
INFO:     Started server process [9716]
INFO:     Waiting for application startup.
INFO:app.main:Starting MyVillage API Gateway
INFO:app.main:Service URLs: {'onboarding': 'http://localhost:8001', 'gemini': 'http://localhost:8002', 'mcp': 'http://localhost:8003'}
INFO:     Application startup complete.
