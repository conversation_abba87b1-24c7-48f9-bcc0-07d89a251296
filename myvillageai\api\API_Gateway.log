INFO:     Will watch for changes in these directories: ['D:\\Projects\\myvillage\\Git_Repo\\myvillageai\\myvillageai\\api']
INFO:     Uvicorn running on http://127.0.0.1:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [2216] using WatchFiles
INFO:     Started server process [10576]
INFO:     Waiting for application startup.
INFO:app.main:Starting MyVillage API Gateway
INFO:app.main:Service URLs: {'onboarding': 'http://localhost:8001', 'gemini': 'http://localhost:8002', 'mcp': 'http://localhost:8003'}
INFO:     Application startup complete.
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           INFO:     127.0.0.1:60872 - "OPTIONS /gemini-chat-with-intent HTTP/1.1" 500 Internal Server Error
ERROR:    Exception in ASGI application
  + Exception Group Traceback (most recent call last):
  |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\_utils.py", line 79, in collapse_excgroups
  |     yield
  |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 183, in __call__
  |     async with anyio.create_task_group() as task_group:
  |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\anyio\_backends\_asyncio.py", line 781, in __aexit__
  |     raise BaseExceptionGroup(
  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)
  +-+---------------- 1 ----------------
    | Traceback (most recent call last):
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\uvicorn\protocols\http\httptools_impl.py", line 409, in run_asgi
    |     result = await app(  # type: ignore[func-returns-value]
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\uvicorn\middleware\proxy_headers.py", line 60, in __call__
    |     return await self.app(scope, receive, send)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\fastapi\applications.py", line 1133, in __call__
    |     await super().__call__(scope, receive, send)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\applications.py", line 113, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\errors.py", line 186, in __call__
    |     raise exc
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\errors.py", line 164, in __call__
    |     await self.app(scope, receive, _send)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\contextlib.py", line 155, in __exit__
    |     self.gen.throw(typ, value, traceback)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\_utils.py", line 85, in collapse_excgroups
    |     raise exc
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "D:\Projects\myvillage\Git_Repo\myvillageai\myvillageai\api\app\main.py", line 120, in reverse_proxy
    |     target_url = f"{service_url}{path[len(matched_prefix):] or '/'}"
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1527, in request
    |     request = self.build_request(
    |               ^^^^^^^^^^^^^^^^^^^
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 366, in build_request
    |     url = self._merge_url(url)
    |           ^^^^^^^^^^^^^^^^^^^^
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 396, in _merge_url
    |     merge_url = URL(url)
    |                 ^^^^^^^^
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_urls.py", line 117, in __init__
    |     self._uri_reference = urlparse(url, **kwargs)
    |                           ^^^^^^^^^^^^^^^^^^^^^^^
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_urlparse.py", line 321, in urlparse
    |     parsed_port: int | None = normalize_port(port, scheme)
    |                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_urlparse.py", line 411, in normalize_port
    |     raise InvalidURL(f"Invalid port: {port!r}")
    | httpx.InvalidURL: Invalid port: '8002-chat-with-intent'
    +------------------------------------

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\uvicorn\protocols\http\httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\uvicorn\middleware\proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\fastapi\applications.py", line 1133, in __call__
    await super().__call__(scope, receive, send)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\errors.py", line 186, in __call__
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\contextlib.py", line 155, in __exit__
    self.gen.throw(typ, value, traceback)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\_utils.py", line 85, in collapse_excgroups
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Projects\myvillage\Git_Repo\myvillageai\myvillageai\api\app\main.py", line 120, in reverse_proxy
    target_url = f"{service_url}{path[len(matched_prefix):] or '/'}"
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1527, in request
    request = self.build_request(
              ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 366, in build_request
    url = self._merge_url(url)
          ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 396, in _merge_url
    merge_url = URL(url)
                ^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_urls.py", line 117, in __init__
    self._uri_reference = urlparse(url, **kwargs)
                          ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_urlparse.py", line 321, in urlparse
    parsed_port: int | None = normalize_port(port, scheme)
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_urlparse.py", line 411, in normalize_port
    raise InvalidURL(f"Invalid port: {port!r}")
httpx.InvalidURL: Invalid port: '8002-chat-with-intent'
INFO:     127.0.0.1:62083 - "OPTIONS /gemini-chat-with-intent HTTP/1.1" 500 Internal Server Error
ERROR:    Exception in ASGI application
  + Exception Group Traceback (most recent call last):
  |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\_utils.py", line 79, in collapse_excgroups
  |     yield
  |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 183, in __call__
  |     async with anyio.create_task_group() as task_group:
  |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\anyio\_backends\_asyncio.py", line 781, in __aexit__
  |     raise BaseExceptionGroup(
  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)
  +-+---------------- 1 ----------------
    | Traceback (most recent call last):
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\uvicorn\protocols\http\httptools_impl.py", line 409, in run_asgi
    |     result = await app(  # type: ignore[func-returns-value]
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\uvicorn\middleware\proxy_headers.py", line 60, in __call__
    |     return await self.app(scope, receive, send)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\fastapi\applications.py", line 1133, in __call__
    |     await super().__call__(scope, receive, send)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\applications.py", line 113, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\errors.py", line 186, in __call__
    |     raise exc
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\errors.py", line 164, in __call__
    |     await self.app(scope, receive, _send)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\contextlib.py", line 155, in __exit__
    |     self.gen.throw(typ, value, traceback)
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\_utils.py", line 85, in collapse_excgroups
    |     raise exc
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "D:\Projects\myvillage\Git_Repo\myvillageai\myvillageai\api\app\main.py", line 120, in reverse_proxy
    |     target_url = f"{service_url}{path[len(matched_prefix):] or '/'}"
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1527, in request
    |     request = self.build_request(
    |               ^^^^^^^^^^^^^^^^^^^
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 366, in build_request
    |     url = self._merge_url(url)
    |           ^^^^^^^^^^^^^^^^^^^^
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 396, in _merge_url
    |     merge_url = URL(url)
    |                 ^^^^^^^^
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_urls.py", line 117, in __init__
    |     self._uri_reference = urlparse(url, **kwargs)
    |                           ^^^^^^^^^^^^^^^^^^^^^^^
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_urlparse.py", line 321, in urlparse
    |     parsed_port: int | None = normalize_port(port, scheme)
    |                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_urlparse.py", line 411, in normalize_port
    |     raise InvalidURL(f"Invalid port: {port!r}")
    | httpx.InvalidURL: Invalid port: '8002-chat-with-intent'
    +------------------------------------

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\uvicorn\protocols\http\httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\uvicorn\middleware\proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\fastapi\applications.py", line 1133, in __call__
    await super().__call__(scope, receive, send)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\errors.py", line 186, in __call__
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\contextlib.py", line 155, in __exit__
    self.gen.throw(typ, value, traceback)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\_utils.py", line 85, in collapse_excgroups
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\starlette\middleware\base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Projects\myvillage\Git_Repo\myvillageai\myvillageai\api\app\main.py", line 120, in reverse_proxy
    target_url = f"{service_url}{path[len(matched_prefix):] or '/'}"
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 1527, in request
    request = self.build_request(
              ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 366, in build_request
    url = self._merge_url(url)
          ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_client.py", line 396, in _merge_url
    merge_url = URL(url)
                ^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_urls.py", line 117, in __init__
    self._uri_reference = urlparse(url, **kwargs)
                          ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_urlparse.py", line 321, in urlparse
    parsed_port: int | None = normalize_port(port, scheme)
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\httpx\_urlparse.py", line 411, in normalize_port
    raise InvalidURL(f"Invalid port: {port!r}")
httpx.InvalidURL: Invalid port: '8002-chat-with-intent'
