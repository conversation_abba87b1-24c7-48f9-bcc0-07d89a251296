"""
Session management service for user flows.

This module handles session state for signup/login flows.
"""

from typing import Dict, Optional, Any
from datetime import datetime, timezone

from ..core.logging import get_logger
from ..core.exceptions import SessionError
from ..models.session import SessionData, FlowType, FlowStep, UserData
from .city_service import get_city_service

logger = get_logger(__name__)


class SessionService:
    """Service for managing user session state."""
    
    def __init__(self):
        """Initialize the session service."""
        # In-memory session storage
        # TODO: Replace with Redis or database for production
        self._sessions: Dict[str, SessionData] = {}
    
    def get_session(self, session_id: str) -> SessionData:
        """
        Get session data for the given session ID.
        
        Args:
            session_id: Unique session identifier
            
        Returns:
            Session data
        """
        if session_id not in self._sessions:
            logger.debug(f"Creating new session: {session_id}")
            self._sessions[session_id] = SessionData(
                created_at=datetime.now(timezone.utc).isoformat(),
                last_activity=datetime.now(timezone.utc).isoformat()
            )
        else:
            # Update last activity
            self._sessions[session_id].last_activity = datetime.now(timezone.utc).isoformat()
        
        return self._sessions[session_id]
    
    def update_session(self, session_id: str, session_data: SessionData) -> None:
        """
        Update session data.
        
        Args:
            session_id: Unique session identifier
            session_data: Updated session data
        """
        session_data.last_activity = datetime.now(timezone.utc).isoformat()
        self._sessions[session_id] = session_data
        logger.debug(f"Updated session: {session_id}")
    
    def start_flow(self, session_id: str, flow_type: FlowType) -> SessionData:
        """
        Start a new user flow.
        
        Args:
            session_id: Unique session identifier
            flow_type: Type of flow to start
            
        Returns:
            Updated session data
        """
        session = self.get_session(session_id)
        
        # Determine starting step based on flow type
        if flow_type == FlowType.SIGNUP:
            session.flow_step = FlowStep.FIRST_NAME
        elif flow_type == FlowType.LOGIN:
            session.flow_step = FlowStep.EMAIL
        else:
            raise SessionError(f"Unknown flow type: {flow_type}")
        
        session.flow_type = flow_type
        session.collected_data = {}
        
        self.update_session(session_id, session)
        
        logger.info(f"Started {flow_type} flow for session: {session_id}")
        
        return session
    
    def advance_flow(
        self,
        session_id: str,
        field_name: str,
        field_value: str
    ) -> Optional[FlowStep]:
        """
        Advance the flow to the next step.
        
        Args:
            session_id: Unique session identifier
            field_name: Name of the field being collected
            field_value: Value of the field
            
        Returns:
            Next flow step, or None if flow is complete
        """
        session = self.get_session(session_id)
        
        if not session.flow_step or not session.flow_type:
            raise SessionError("No active flow for session")
        
        # Validate field value before storing
        validated_value = self._validate_field_value(field_name, field_value, session_id)

        # Store the collected data (only if not None/skipped)
        if validated_value is not None:
            session.collected_data[field_name] = validated_value
            logger.info(f"FLOW DEBUG: Stored field '{field_name}' = '{validated_value}' for session {session_id}")
            
            # If we just got first or last name, update the full name
            if field_name in ["firstName", "lastName"]:
                first_name = session.collected_data.get("firstName", "")
                last_name = session.collected_data.get("lastName", "")
                if first_name or last_name:
                    session.collected_data["name"] = f"{first_name} {last_name}".strip()
                    logger.info(f"FLOW DEBUG: Auto-set name='{session.collected_data['name']}' from first/last name")
        else:
            logger.info(f"FLOW DEBUG: Skipped field '{field_name}' for session {session_id}")
            
        # Set default values for specific fields without user input
        if field_name == "userAddedFrom" and "userAddedFrom" not in session.collected_data:
            session.collected_data["userAddedFrom"] = "self_registration"
            logger.info(f"FLOW DEBUG: Auto-set userAddedFrom='self_registration' for session {session_id}")
            
        if field_name == "registeredFrom" and "registeredFrom" not in session.collected_data:
            session.collected_data["registeredFrom"] = "WEB"
            logger.info(f"FLOW DEBUG: Auto-set registeredFrom='WEB' for session {session_id}")
            
        # Set default role and userType
        # Set default values for role and assignedRole
        if field_name in ["role", "assignedRole"] and "role" not in session.collected_data:
            session.collected_data["role"] = "MEMBER"
            session.collected_data["assignedRole"] = "MEMBER"
            logger.info(f"FLOW DEBUG: Auto-set role and assignedRole='MEMBER' for session {session_id}")
            
        if field_name == "userType" and "userType" not in session.collected_data:
            session.collected_data["userType"] = "loginUser"
            logger.info(f"FLOW DEBUG: Auto-set userType='loginUser' for session {session_id}")

        logger.info(f"FLOW DEBUG: Current collected data keys: {list(session.collected_data.keys())}")
        
        # Determine next step
        current_step = session.flow_step
        flow_type = session.flow_type
        
        if flow_type == FlowType.SIGNUP:
            # Define the signup flow sequence
            signup_flow = [
                FlowStep.FIRST_NAME,  # Start with first name
                FlowStep.LAST_NAME,   # Then last name
                FlowStep.EMAIL,
                FlowStep.PASSWORD,
                FlowStep.PHONE_NUMBER,  # Now required
                FlowStep.CITY_NAME,  # Changed from CITY_ID to CITY_NAME
                FlowStep.STATE,
                FlowStep.ZIP_CODE,
                FlowStep.STREET_ADDRESS_ONE,
                FlowStep.STREET_ADDRESS_TWO,
                FlowStep.TYPE,
                FlowStep.CREATED_BY,
                FlowStep.IS_STAKEHOLDER,
                FlowStep.IS_ASSOCIATED,
                FlowStep.GENDER,
                FlowStep.BIRTHDAY,
                FlowStep.CITY_NAMES_ARRAY,  # Changed from CITIES_ARRAY to CITY_NAMES_ARRAY
                FlowStep.CONFIRM  # Confirmation step at the end
            ]

            logger.debug(f"SIGNUP FLOW DEBUG: current_step={current_step}, total_steps={len(signup_flow)}")

            # Ensure we can compare the current step correctly whether it's an
            # enum member or a plain string value (defensive - sessions may be
            # mutated or serialized elsewhere).
            try:
                if isinstance(current_step, str):
                    # Try to coerce string to FlowStep enum
                    current_step_enum = FlowStep(current_step)
                else:
                    current_step_enum = current_step

                # Find the index of the current step in the defined flow
                current_index = next(i for i, s in enumerate(signup_flow) if s == current_step_enum)
                logger.debug(f"SIGNUP FLOW DEBUG: current_index={current_index}")

                if current_index + 1 < len(signup_flow):
                    next_step = signup_flow[current_index + 1]
                    session.flow_step = next_step
                    logger.debug(f"SIGNUP FLOW DEBUG: advancing to step {current_index + 1}: {next_step}")
                else:
                    session.flow_step = None  # Flow complete
                    logger.debug(f"SIGNUP FLOW DEBUG: flow complete after {len(signup_flow)} steps")

            except Exception as e:
                # If we couldn't resolve the current step, raise a SessionError
                # instead of silently completing the flow. This prevents
                # premature completion when the step is unexpected.
                logger.warning(f"SIGNUP FLOW DEBUG: failed to advance flow from current_step={current_step}: {e}")
                raise SessionError(f"Invalid current flow step: {current_step}")
        
        elif flow_type == FlowType.LOGIN:
            if current_step == FlowStep.EMAIL:
                session.flow_step = FlowStep.PASSWORD
            elif current_step == FlowStep.PASSWORD:
                session.flow_step = None  # Flow complete
        
        self.update_session(session_id, session)
        
        logger.debug(f"Advanced flow for session {session_id}: {current_step} -> {session.flow_step}")
        
        return session.flow_step
    
    def complete_flow(self, session_id: str) -> UserData:
        """
        Complete the current flow and return collected data.
        
        Args:
            session_id: Unique session identifier
            
        Returns:
            Collected user data
        """
        session = self.get_session(session_id)
        
        if not session.flow_type:
            raise SessionError("No active flow to complete")
        
        # Create user data from collected data
        logger.debug(f"Creating UserData from collected data: {session.collected_data}")
        try:
            user_data = UserData(**session.collected_data)
            logger.debug(f"Successfully created UserData: name={user_data.name}, email={user_data.email}, password={'***' if user_data.password else None}")
        except Exception as e:
            logger.error(f"Failed to create UserData from collected data {session.collected_data}: {e}")
            raise SessionError(f"Invalid user data collected: {str(e)}")
        
        if session.flow_type == FlowType.SIGNUP:
            # For signup flow, we need to validate required fields
            has_name = user_data.name or (user_data.firstName and user_data.lastName)
            if not has_name or not user_data.email or not user_data.password:
                logger.error(f"Missing required signup data: name={user_data.name}, firstName={user_data.firstName}, lastName={user_data.lastName}, email={user_data.email}, password={'***' if user_data.password else None}")
                raise SessionError("Missing required signup information")
            
            # If we're at the confirmation step, handle confirmation
            if session.flow_step == FlowStep.CONFIRM:
                if 'is_confirmed' in session.collected_data and session.collected_data['is_confirmed'].lower() in ['yes', 'y', 'true', '1']:
                    user_data.is_confirmed = True
                    session.is_confirmed = True
                    logger.info(f"User confirmed signup details for session: {session_id}")
                else:
                    # If user doesn't confirm, reset to the first step
                    session.flow_step = FlowStep.FIRST_NAME
                    session.collected_data = {}  # Clear collected data to start over
                    self.update_session(session_id, session)
                    logger.info(f"User requested to re-enter information for session: {session_id}")
                    raise SessionError("Please provide the information again starting with your first name.")
        
        # Reset session if flow is complete
        if session.flow_step is None or session.flow_step == FlowStep.CONFIRM:
            session.flow_step = None
            session.flow_type = None
            session.collected_data = {}
            self.update_session(session_id, session)
            logger.info(f"Completed {session.flow_type} flow for session: {session_id}")
        
        return user_data
    
    def reset_session(self, session_id: str) -> None:
        """
        Reset session state.
        
        Args:
            session_id: Unique session identifier
        """
        if session_id in self._sessions:
            del self._sessions[session_id]
            logger.debug(f"Reset session: {session_id}")
    
    def is_flow_active(self, session_id: str) -> bool:
        """
        Check if there's an active flow for the session.
        
        Args:
            session_id: Unique session identifier
            
        Returns:
            True if flow is active
        """
        session = self.get_session(session_id)
        return session.flow_step is not None and session.flow_type is not None

    def _validate_field_value(self, field_name: str, field_value: str, session_id: str = None) -> str:
        """
        Validate field value based on field type.

        Args:
            field_name: Name of the field being validated
            field_value: Value to validate

        Returns:
            Validated and cleaned field value

        Raises:
            SessionError: If validation fails
        """
        from ..utils.security import validate_email, sanitize_input
        import re

        # Clean the input first
        cleaned_value = sanitize_input(field_value.strip())

        # Handle skip for optional fields
        if cleaned_value.lower() == 'skip' and field_name not in ["phoneNumber", "firstName", "lastName"]:
            return None

        # Required fields validation
        required_fields = ["name", "firstName", "lastName", "email", "password", "phoneNumber"]
        if field_name in required_fields and not cleaned_value:
            raise SessionError(
                f"{field_name} is required and cannot be empty",
                details={"field": field_name}
            )

        # Specific field validations
        if field_name == "email":
            if not validate_email(cleaned_value):
                raise SessionError(
                    f"Invalid email format. Please provide a valid email address (e.g., <EMAIL>)",
                    details={"field": "email", "value": cleaned_value}
                )
            return cleaned_value.lower()

        elif field_name == "password":
            if len(cleaned_value) < 1:
                raise SessionError(
                    "Password cannot be empty",
                    details={"field": "password"}
                )
            return cleaned_value

        elif field_name in ["firstName", "lastName"]:
            if len(cleaned_value) < 1:
                raise SessionError(
                    f"{field_name} cannot be empty",
                    details={"field": field_name}
                )
            return cleaned_value
            
        elif field_name == "name":
            # This will be set automatically from first and last name
            return cleaned_value

        elif field_name == "phoneNumber":
            # Phone number is now required
            phone_cleaned = re.sub(r'[^\d+\-\(\)\s]', '', cleaned_value)
            if not re.match(r'^[\+\-]?[\d\-\(\)\s]+$', phone_cleaned):
                raise SessionError(
                    "Invalid phone number format. Please use digits, +, -, (), or spaces only",
                    details={"field": "phoneNumber", "value": cleaned_value}
                )
            return phone_cleaned

        elif field_name == "zipCode":
            if cleaned_value:
                # Basic ZIP code validation
                if not re.match(r'^\d{5}(-\d{4})?$', cleaned_value):
                    raise SessionError(
                        "Invalid ZIP code format. Please use format 12345 or 12345-6789",
                        details={"field": "zipCode", "value": cleaned_value}
                    )
            return cleaned_value if cleaned_value else None

        elif field_name == "birthday":
            if cleaned_value:
                # Validate YYYY-MM-DD format
                if not re.match(r'^\d{4}-\d{2}-\d{2}$', cleaned_value):
                    raise SessionError(
                        "Invalid birthday format. Please use YYYY-MM-DD format (e.g., 1990-01-01)",
                        details={"field": "birthday", "value": cleaned_value}
                    )
            return cleaned_value if cleaned_value else None

        elif field_name in ["isStakeholder", "isAssociated"]:
            if cleaned_value:
                if cleaned_value.lower() in ['yes', 'y', 'true', '1']:
                    return True
                elif cleaned_value.lower() in ['no', 'n', 'false', '0']:
                    return False
                else:
                    raise SessionError(
                        f"Please answer yes or no for {field_name}",
                        details={"field": field_name, "value": cleaned_value}
                    )
            return None

        elif field_name == "cityName":
            if not cleaned_value:
                return None

            # Validate city name and get cityId
            city_service = get_city_service()
            city_id = city_service.validate_city_name_and_get_id(cleaned_value)

            if city_id is None:
                raise SessionError(
                    f"City '{cleaned_value}' not found. Please enter a valid city name.",
                    details={"field": "cityName", "value": cleaned_value}
                )

            # Store both the city name and the resolved cityId in the session
            session = self.get_session(session_id)
            session.collected_data["cityId"] = city_id
            session.collected_data["city"] = cleaned_value  # For backward compatibility
            logger.info(f"Validated city name '{cleaned_value}' -> cityId: {city_id}")

            return cleaned_value

        elif field_name == "cityNamesArray":
            if not cleaned_value:
                return None

            try:
                # Clean up the input by removing HTML entities and extra quotes
                cleaned = (
                    cleaned_value
                    .replace('&quot;', '')  # Remove HTML entity &quot;
                    .replace('"', '')      # Remove any remaining quotes
                    .replace("'", '')      # Remove single quotes
                    .strip('[]')            # Remove square brackets
                    .strip()
                )

                # Split by comma and clean each city name
                city_names = [city.strip() for city in cleaned.split(',') if city.strip()]

                if not city_names:
                    return None

                # Validate each city name and get cityIds
                city_service = get_city_service()
                validation_results = city_service.validate_city_names_and_get_ids(city_names)

                # Check for invalid city names
                invalid_cities = [name for name, city_id in validation_results.items() if city_id is None]
                if invalid_cities:
                    raise SessionError(
                        f"The following cities were not found: {', '.join(invalid_cities)}. Please enter valid city names.",
                        details={"field": "cityNamesArray", "value": cleaned_value, "invalid_cities": invalid_cities}
                    )

                # Store the resolved cityIds in the session
                session = self.get_session(session_id)
                city_ids = [city_id for city_id in validation_results.values() if city_id is not None]
                session.collected_data["citiesArray"] = city_ids
                logger.info(f"Validated city names {city_names} -> cityIds: {city_ids}")

                return city_names

            except SessionError:
                # Re-raise SessionError as-is
                raise
            except Exception as e:
                logger.error(f"Error processing cityNamesArray: {str(e)}")
                raise SessionError(
                    "Please provide valid city names separated by commas",
                    details={"field": "cityNamesArray", "value": cleaned_value}
                )

        elif field_name == "citiesArray":
            if not cleaned_value:
                return None
                
            try:
                # Clean up the input by removing HTML entities and extra quotes
                cleaned = (
                    cleaned_value
                    .replace('&quot;', '')  # Remove HTML entity &quot;
                    .replace('"', '')      # Remove any remaining quotes
                    .replace("'", '')      # Remove single quotes
                    .strip('[]')            # Remove square brackets
                    .strip()
                )
                
                # Split by comma and clean each city ID
                cities = [city.strip() for city in cleaned.split(',') if city.strip()]
                
                # Validate each city ID is a valid UUID
                import uuid
                for city_id in cities:
                    try:
                        uuid.UUID(city_id)
                    except ValueError:
                        raise SessionError(
                            f"Invalid city ID format: {city_id}",
                            details={"field": "citiesArray", "value": cleaned_value}
                        )
                
                return cities if cities else None
                
            except Exception as e:
                logger.error(f"Error processing citiesArray: {str(e)}")
                raise SessionError(
                    "Please provide valid city IDs separated by commas",
                    details={"field": "citiesArray", "value": cleaned_value}
                )

        # For other optional fields, return cleaned value or None
        return cleaned_value if cleaned_value else None
    
    def get_flow_message(self, session_id: str) -> str:
        """
        Get the appropriate message for the current flow step.
        
        Args:
            session_id: Unique session identifier
            
        Returns:
            Flow message for the user
        """
        session = self.get_session(session_id)
        
        if not session.flow_step or not session.flow_type:
            return "No active flow"
        
        flow_type = session.flow_type.value
        step = session.flow_step.value
        
        if flow_type == "signup" and step == "confirm":
            # Build confirmation message with all collected data
            user_data = UserData(**session.collected_data)
            
            # Format the confirmation message
            confirmation = "Please review your information:\n\n"
            
            # Basic info
            if user_data.name:
                confirmation += f"👤 Name: {user_data.name}\n"
            if user_data.firstName or user_data.lastName:
                name_parts = []
                if user_data.firstName:
                    name_parts.append(user_data.firstName)
                if user_data.lastName:
                    name_parts.append(user_data.lastName)
                if name_parts:
                    confirmation += f"👤 Name: {' '.join(name_parts)}\n"
            
            # Contact info
            if user_data.email:
                confirmation += f"📧 Email: {user_data.email}\n"
            if user_data.phoneNumber:
                confirmation += f"📱 Phone: {user_data.phoneNumber}\n"
            
            # Address
            address_parts = []
            if user_data.streetAddressOne:
                address_parts.append(user_data.streetAddressOne)
            if user_data.streetAddressTwo and user_data.streetAddressTwo.lower() != 'skip':
                address_parts.append(user_data.streetAddressTwo)
            
            location_parts = []
            if user_data.city and user_data.city.lower() != 'skip':
                location_parts.append(user_data.city)
            if user_data.state and user_data.state.lower() != 'skip':
                location_parts.append(user_data.state)
            if user_data.zipCode and user_data.zipCode.lower() != 'skip':
                location_parts.append(user_data.zipCode)
            
            if address_parts:
                confirmation += "\n🏠 Address:\n"
                confirmation += "\n".join([f"- {part}" for part in address_parts])
                if location_parts:
                    confirmation += "\n" + ", ".join(location_parts)
            
            # Additional info
            additional_info = []
            if user_data.role and user_data.role.lower() != 'skip':
                additional_info.append(f"Role: {user_data.role}")
            if user_data.userType and user_data.userType.lower() != 'skip':
                additional_info.append(f"User Type: {user_data.userType}")
            if user_data.gender and user_data.gender.lower() != 'skip':
                additional_info.append(f"Gender: {user_data.gender}")
            if user_data.birthday and user_data.birthday.lower() != 'skip':
                additional_info.append(f"Birthday: {user_data.birthday}")
            
            if additional_info:
                confirmation += "\n\nℹ️ Additional Information:\n"
                confirmation += "\n".join([f"- {info}" for info in additional_info])
            
            confirmation += "\n\nIs this information correct? (yes/no)"
            return confirmation
            
        messages = {
            # Basic information
            ("signup", "name"): "Let's start signup! Please enter your full name.",
            ("signup", "firstName"): "Let's start signup! Please enter your first name.",
            ("signup", "lastName"): "Please enter your last name.",
            ("signup", "email"): "Please enter your email address.",
            ("signup", "password"): "Please create a password (at least 8 characters with letters and numbers):",

            # Contact information - phone is now required
            ("signup", "phoneNumber"): "Please enter your phone number.",

            # Location information
            ("signup", "cityId"): "Please enter your city ID (optional, or type 'skip').",
            ("signup", "cityName"): "Please enter your city name (e.g., 'New York', 'Los Angeles'). We'll validate it for you.",
            ("signup", "city"): "Please enter your city name (optional, or type 'skip').",
            ("signup", "state"): "Please enter your state or province (optional, or type 'skip').",
            ("signup", "zipCode"): "Please enter your ZIP or postal code (optional, or type 'skip').",
            ("signup", "streetAddressOne"): "Please enter your street address (optional, or type 'skip').",
            ("signup", "streetAddressTwo"): "Please enter additional address information like apartment number (optional, or type 'skip').",

            # Role and account information - set automatically
            ("signup", "role"): "",  # Will be set to MEMBER automatically
            ("signup", "assignedRole"): "Please enter your assigned role (optional, or type 'skip').",
            ("signup", "userType"): "",  # Will be set to loginUser automatically
            ("signup", "type"): "Please enter your account type (e.g., 'standard', 'premium') (optional, or type 'skip').",

            # Registration metadata - set automatically
            ("signup", "registeredFrom"): "",  # Set to WEB automatically
            ("signup", "userAddedFrom"): "",   # Set to self_registration automatically
            ("signup", "createdBy"): "Please enter who created your account (optional, or type 'skip').",

            # Flags and associations
            ("signup", "isStakeholder"): "Are you a stakeholder? (yes/no, or type 'skip').",
            ("signup", "isAssociated"): "Are you associated with an organization? (yes/no, or type 'skip').",

            # Personal information
            ("signup", "gender"): "Please enter your gender (optional, or type 'skip').",
            ("signup", "birthday"): "Please enter your birthday in YYYY-MM-DD format (optional, or type 'skip').",

            # Multi-city access
            ("signup", "citiesArray"): "Please enter city IDs you have access to, separated by commas (optional, or type 'skip').",
            ("signup", "cityNamesArray"): "Please enter city names you have access to, separated by commas (e.g., 'New York, Los Angeles') (optional, or type 'skip').",
            
            # Confirmation step
            ("signup", "confirm"): "Please confirm your details (this will be handled by the confirmation logic)",

            # Login flow
            ("login", "email"): "Let's start login! Please enter your email.",
            ("login", "password"): "Please enter your password.",
        }
        
        return messages.get((flow_type, step), f"Please provide your {step}")


# Global service instance
session_service = SessionService()
