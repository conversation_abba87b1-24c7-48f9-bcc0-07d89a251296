"""
Authentication endpoints for the onboarding API.

This module handles user authentication including login and token management.
"""

from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from pydantic import BaseModel, EmailStr
from typing import Optional

from app.core.config import settings
from app.core.logging import get_logger
from app.core.exceptions import AuthenticationError
from app.models.requests import LoginRequest
from app.models.responses import AuthenticationResponse
from app.services.auth_service import auth_service

logger = get_logger(__name__)

router = APIRouter(tags=["authentication"])


class Token(BaseModel):
    """Token response model."""
    access_token: str
    token_type: str = "bearer"


@router.post("/login", response_model=Token)
async def login_for_access_token(
    form_data: OAuth2PasswordRequestForm = Depends()
) -> Token:
    """
    OAuth2 compatible token login, get an access token for future requests.
    
    Args:
        form_data: Form data with username and password
        
    Returns:
        Access token and token type
        
    Raises:
        HTTPException: If authentication fails
    """
    try:
        # Create login request
        login_data = LoginRequest(
            email=form_data.username,
            password=form_data.password
        )
        
        # Authenticate user
        auth_result = auth_service.login(login_data)
        
        # In a real implementation, you would generate a JWT token here
        # For now, we'll return a mock token
        return Token(
            access_token="mock-jwt-token-123",
            token_type="bearer"
        )
        
    except AuthenticationError as e:
        logger.warning(f"Authentication failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except Exception as e:
        logger.error(f"Unexpected error during login: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred during authentication"
        )


@router.post("/token", response_model=Token)
async def login_for_token(
    email: str,
    password: str
) -> Token:
    """
    Alternative login endpoint that accepts email/password as form data.
    
    Args:
        email: User's email
        password: User's password
        
    Returns:
        Access token and token type
    """
    return await login_for_access_token(
        OAuth2PasswordRequestForm(
            username=email,
            password=password,
            scope=""
        )
    )
