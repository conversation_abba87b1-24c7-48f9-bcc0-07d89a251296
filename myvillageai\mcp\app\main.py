"""
MCP Service Main Application

This module creates and configures the FastAPI application for the MCP service.
"""

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import logging
import os
from typing import Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan management."""
    # Startup
    logger.info("Starting MCP Service")
    
    # Initialize MCP service
    try:
        # TODO: Initialize MCP service components
        logger.info("MCP service initialized successfully")
        yield
    except Exception as e:
        logger.error(f"Failed to initialize MCP service: {str(e)}")
        raise
    finally:
        # Cleanup
        logger.info("Shutting down MCP Service")

# Create FastAPI app
app = FastAPI(
    title="MyVillage MCP Service",
    description="Model Control Plane service for MyVillage",
    version="0.1.0",
    lifespan=lifespan
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Import and include routers
from .routes import router as mcp_router
app.include_router(mcp_router, prefix="/api/mcp", tags=["mcp"])

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "ok", "service": "mcp"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=8003, reload=True)
