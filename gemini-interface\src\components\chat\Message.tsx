import React from 'react';
import { format } from 'date-fns';
import { Message as MessageType } from '@/types/chat';
import { Avatar } from '@/components/ui/Avatar';
import { cn } from '@/lib/utils';

interface MessageProps {
  message: MessageType;
  isCurrentUser: boolean;
}

export const Message: React.FC<MessageProps> = ({ message, isCurrentUser }) => {
  return (
    <div
      className={cn(
        'flex gap-3 p-4',
        isCurrentUser ? 'justify-end' : 'justify-start'
      )}
    >
      {!isCurrentUser && (
        <Avatar
          src={message.sender.avatar}
          alt={message.sender.name}
          fallback={message.sender.name[0]}
          className="self-end"
        />
      )}
      
      <div
        className={cn(
          'max-w-[80%] rounded-2xl px-4 py-2',
          isCurrentUser
            ? 'bg-primary text-primary-foreground rounded-br-none'
            : 'bg-muted rounded-bl-none',
        )}
      >
        {!isCurrentUser && (
          <div className="font-semibold text-sm mb-1">
            {message.sender.name}
          </div>
        )}
        <div className="whitespace-pre-wrap">{message.content}</div>
        <div
          className={cn(
            'text-xs mt-1',
            isCurrentUser ? 'text-primary-foreground/70' : 'text-muted-foreground',
            'text-right'
          )}
        >
          {format(new Date(message.timestamp), 'h:mm a')}
        </div>
      </div>
      
      {isCurrentUser && (
        <Avatar
          src={message.sender.avatar}
          alt={message.sender.name}
          fallback={message.sender.name[0]}
          className="self-end"
        />
      )}
    </div>
  );
};
