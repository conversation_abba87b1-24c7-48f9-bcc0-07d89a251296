'use client';

import { useEffect, useState } from 'react';

export default function HtmlWrapper({ children }: { children: React.ReactNode }) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    // Apply theme classes to the html element when mounted
    document.documentElement.classList.add('light', 'scroll-smooth');
    document.documentElement.style.colorScheme = 'light';
  }, []);

  return <>{children}</>;
}
