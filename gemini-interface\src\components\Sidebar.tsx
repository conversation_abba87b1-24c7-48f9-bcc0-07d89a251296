"use client";

import { useState, useEffect } from "react";
import {
  X,
  Check,
  RefreshCw,
  Terminal,
  ListChecks,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import { useToolContext } from "@/contexts/ToolContext";
import { useChatContext } from "@/contexts/ChatContext";

type ToolCall = {
  id: string;
  toolName: string;
  status: "pending" | "success" | "error";
  timestamp: number;
  duration?: number;
  error?: string;
  parameters?: Record<string, any>;
};

type Message = {
  id: string;
  role: "user" | "assistant" | "system";
  content: string;
  includeInContext: boolean;
};

type TabType = "tools" | "context";

export default function Sidebar() {
  const [activeTab, setActiveTab] = useState<TabType>("tools");
  const [isOpen, setIsOpen] = useState(() => {
    if (typeof window !== "undefined") {
      const saved = localStorage.getItem("sidebarOpen");
      return saved !== null ? JSON.parse(saved) : true;
    }
    return true;
  });

  useEffect(() => {
    if (typeof window !== "undefined") {
      localStorage.setItem("sidebarOpen", JSON.stringify(isOpen));
    }
  }, [isOpen]);

  // Mock MCP tools data - in a real app, this would come from an API
  const mcpTools = [
    {
      name: "classify_intent",
      description: "Classify the intent of a user message",
      parameters: {
        message: {
          type: "string",
          description: "The user message to classify",
          required: true,
        },
      },
    },
    {
      name: "chat_with_intent",
      description: "Chat with Gemini AI using intent-aware processing",
      parameters: {
        message: {
          type: "string",
          description: "The user message",
          required: true,
        },
        session_id: {
          type: "string",
          description: "Session identifier for conversation context",
          required: true,
        },
      },
    },
    {
      name: "browser_navigate",
      description: "Navigate to a URL in the browser",
      parameters: {
        url: {
          type: "string",
          description: "The URL to navigate to",
          required: true,
        },
      },
    },
    {
      name: "browser_click",
      description: "Click on an element on the page",
      parameters: {
        selector: {
          type: "string",
          description: "CSS selector for the element to click",
          required: true,
        },
      },
    },
  ];

  if (!isOpen) {
    return (
      <button
        onClick={() => setIsOpen(true)}
        className="fixed left-0 top-1/2 -translate-y-1/2 bg-blue-500 text-white p-2 rounded-r-lg z-10 shadow-lg hover:bg-blue-600 transition-colors"
      >
        <ChevronRight className="w-5 h-5" />
      </button>
    );
  }

  // Tool context
  const { toolCalls, clearToolCalls } = useToolContext();

  // Chat context for message management
  const { messages, updateMessage, clearMessages, tokenCount } =
    useChatContext();
  const maxTokens = 4000;
  const tokenPercentage = Math.min(100, (tokenCount / maxTokens) * 100);
  const tokenStatusColor =
    tokenPercentage > 90
      ? "bg-red-500"
      : tokenPercentage > 70
      ? "bg-yellow-500"
      : "bg-blue-500";

  const toggleIncludeMessage = (id: string) => {
    const message = messages.find((m) => m.id === id);
    if (message) {
      updateMessage(id, { includeInContext: !message.includeInContext });
    }
  };

  return (
    <div className="w-80 h-full flex flex-col bg-white border-r border-gray-200">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 flex justify-between items-center">
        <h2 className="text-lg font-semibold">My Village OS</h2>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setIsOpen(false)}
            className="text-gray-500 hover:text-gray-700 p-1 rounded hover:bg-gray-100"
            title="Collapse sidebar"
          >
            <ChevronLeft className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Sidebar Content */}
      <div className="flex-1 overflow-y-auto p-4 space-y-6">
        {activeTab === "tools" ? (
          <>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-sm font-medium text-gray-900">
                  Available MCP Tools
                </h3>
              </div>
              <div className="space-y-2">
                {mcpTools.map((tool, index) => (
                  <div
                    key={index}
                    className="p-3 bg-gray-50 rounded-lg border border-gray-200"
                  >
                    <h4 className="text-sm font-medium text-gray-900">
                      {tool.name}
                    </h4>
                    <p className="text-xs text-gray-600 mt-1">
                      {tool.description}
                    </p>
                    {tool.parameters && (
                      <div className="mt-2">
                        <p className="text-xs font-medium text-gray-700 mb-1">
                          Parameters:
                        </p>
                        <ul className="text-xs text-gray-600 space-y-1">
                          {Object.entries(tool.parameters).map(
                            ([name, param]: [string, any]) => (
                              <li key={name} className="flex justify-between">
                                <span className="font-mono">{name}</span>
                                <span className="text-gray-500">
                                  {param.type}
                                  {param.required ? " (required)" : ""}
                                </span>
                              </li>
                            )
                          )}
                        </ul>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            <div className="space-y-4 pt-4 border-t border-gray-200">
              <div className="flex justify-between items-center">
                <h3 className="text-sm font-medium text-gray-900">
                  Tool Usage
                </h3>
                <button
                  onClick={clearToolCalls}
                  className="text-xs text-gray-500 hover:text-gray-700 flex items-center gap-1"
                >
                  <RefreshCw className="w-3 h-3" />
                  Clear all
                </button>
              </div>
              {toolCalls.length === 0 ? (
                <p className="text-sm text-gray-500 text-center py-4">
                  No tool usage recorded yet
                </p>
              ) : (
                <div className="space-y-2">
                  {toolCalls.map((tool) => (
                    <div
                      key={tool.id}
                      className="p-3 bg-gray-50 rounded-lg border border-gray-200"
                    >
                      <div className="flex justify-between items-start">
                        <div>
                          <h4 className="text-sm font-medium text-gray-900">
                            {tool.toolName}
                          </h4>
                          <p className="text-xs text-gray-500 mt-1">
                            {new Date(tool.timestamp).toLocaleTimeString()}
                            {tool.duration && ` • ${tool.duration}ms`}
                          </p>
                        </div>
                        <span
                          className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                            tool.status === "success"
                              ? "bg-green-100 text-green-800"
                              : tool.status === "error"
                              ? "bg-red-100 text-red-800"
                              : "bg-yellow-100 text-yellow-800"
                          }`}
                        >
                          {tool.status}
                        </span>
                      </div>
                      {tool.error && (
                        <p className="mt-2 text-xs text-red-600">
                          {tool.error}
                        </p>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </>
        ) : (
          <div className="p-3">
            <div className="mb-3">
              <div className="flex justify-between text-xs mb-1">
                <span>Context Usage</span>
                <span>
                  {tokenCount} / {maxTokens} tokens
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className={`h-2 rounded-full ${tokenStatusColor}`}
                  style={{ width: `${tokenPercentage}%` }}
                />
              </div>
            </div>

            <div className="space-y-2 max-h-[calc(100vh-200px)] overflow-y-auto">
              {messages.map((message) => (
                <div
                  key={message.id}
                  className="p-2 border rounded-lg hover:bg-gray-50 text-sm"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center text-xs mb-1">
                        <span
                          className={`font-medium ${
                            message.role === "user"
                              ? "text-blue-600"
                              : message.role === "assistant"
                              ? "text-green-600"
                              : "text-purple-600"
                          }`}
                        >
                          {message.role.toUpperCase()}
                        </span>
                      </div>
                      <p className="text-gray-700 line-clamp-2">
                        {message.content}
                      </p>
                    </div>
                    <button
                      onClick={() => toggleIncludeMessage(message.id)}
                      className={`ml-2 p-1 rounded-full ${
                        message.includeInContext
                          ? "text-green-500 hover:bg-green-50"
                          : "text-gray-400 hover:bg-gray-100"
                      }`}
                      title={
                        message.includeInContext
                          ? "Exclude from context"
                          : "Include in context"
                      }
                    >
                      {message.includeInContext ? (
                        <Check className="w-4 h-4" />
                      ) : (
                        <X className="w-4 h-4" />
                      )}
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Sidebar Footer */}
      <div className="p-2 border-t border-gray-200 text-xs text-gray-500 bg-gray-50">
        {activeTab === "tools"
          ? `${toolCalls.length} tool calls`
          : `${messages.filter((m) => m.includeInContext).length} of ${
              messages.length
            } messages in context`}
      </div>
    </div>
  );
}
