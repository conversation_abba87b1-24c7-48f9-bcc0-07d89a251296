'use client';

import { useEffect, useState } from 'react';

type ToastType = {
  id: string;
  message: string;
  type: 'default' | 'success' | 'error' | 'warning' | 'info';
  duration: number;
};

const TOAST_DURATION = 5000; // 5 seconds

// Create a simple toast manager
const toastManager = (() => {
  let toastId = 0;
  let updateToasts: ((toasts: ToastType[]) => void) | null = null;
  let toasts: ToastType[] = [];

  const addToast = (message: string, type: ToastType['type'] = 'default', duration = TOAST_DURATION) => {
    const id = `toast-${toastId++}`;
    const newToast: ToastType = { id, message, type, duration };
    
    toasts = [...toasts, newToast];
    updateToasts?.([...toasts]);

    setTimeout(() => {
      removeToast(id);
    }, duration);

    return id;
  };

  const removeToast = (id: string) => {
    toasts = toasts.filter(toast => toast.id !== id);
    updateToasts?.([...toasts]);
  };

  return {
    subscribe: (callback: (toasts: ToastType[]) => void) => {
      updateToasts = callback;
      return () => {
        updateToasts = null;
      };
    },
    toasts: () => [...toasts],
    add: addToast,
    remove: removeToast,
  };
})();

// Export the toast function
export const toast = Object.assign(
  (message: string, type: ToastType['type'] = 'default', duration?: number) => 
    toastManager.add(message, type, duration),
  {
    success: (message: string, duration?: number) => toastManager.add(message, 'success', duration),
    error: (message: string, duration?: number) => toastManager.add(message, 'error', duration),
    warning: (message: string, duration?: number) => toastManager.add(message, 'warning', duration),
    info: (message: string, duration?: number) => toastManager.add(message, 'info', duration),
    remove: (id: string) => toastManager.remove(id),
  }
);

// Toast component
export default function Toaster() {
  const [toasts, setToasts] = useState<ToastType[]>([]);

  useEffect(() => {
    // Subscribe to toast updates
    const unsubscribe = toastManager.subscribe((newToasts) => {
      setToasts(newToasts);
    });

    // Cleanup
    return () => {
      unsubscribe();
    };
  }, []);

  const getToastStyles = (type: ToastType['type']) => {
    const baseStyles = 'px-4 py-3 rounded-md shadow-md text-sm font-medium transition-all duration-300 transform';
    const typeStyles = {
      default: 'bg-white text-gray-800 border border-gray-200',
      success: 'bg-green-100 text-green-800 border border-green-200',
      error: 'bg-red-100 text-red-800 border border-red-200',
      warning: 'bg-yellow-100 text-yellow-800 border border-yellow-200',
      info: 'bg-blue-100 text-blue-800 border border-blue-200',
    };
    
    return `${baseStyles} ${typeStyles[type]}`;
  };

  if (toasts.length === 0) return null;

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 w-80">
      {toasts.map((toast) => (
        <div
          key={toast.id}
          className={getToastStyles(toast.type)}
          style={{
            animation: 'slideIn 0.3s ease-out',
          }}
          onClick={() => toastManager.remove(toast.id)}
        >
          <div className="flex items-center justify-between">
            <span>{toast.message}</span>
            <button 
              className="ml-2 text-gray-500 hover:text-gray-700"
              onClick={(e) => {
                e.stopPropagation();
                toastManager.remove(toast.id);
              }}
            >
              ✕
            </button>
          </div>
        </div>
      ))}
      <style jsx global>{`
        @keyframes slideIn {
          from {
            transform: translateX(100%);
            opacity: 0;
          }
          to {
            transform: translateX(0);
            opacity: 1;
          }
        }
      `}</style>
    </div>
  );
}
