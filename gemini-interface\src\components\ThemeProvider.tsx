'use client';

import { useEffect, useState } from 'react';

export default function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Don't render anything until we're on the client to avoid hydration mismatch
  if (!mounted) {
    return <>{children}</>;
  }

  return (
    <html lang="en" className="scroll-smooth light" style={{ colorScheme: 'light' }}>
      {children}
    </html>
  );
}
