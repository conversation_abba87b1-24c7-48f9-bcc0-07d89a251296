{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_11995f80._.js", "server/edge/chunks/[root-of-the-server]__8978dbac._.js", "server/edge/chunks/turbopack-edge-wrapper_0012b839.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/api/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "QIlue0ecL2aUrzqResBorzr1IdYmkezk+eCiy28QzQ4=", "__NEXT_PREVIEW_MODE_ID": "b4250687ec0c1f7b3d88224eaaeaa856", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "8f4a5f01b2e88ab6a23291217eb82dec3844f65c1aad55b9c848c31850aba178", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "3cd057094a98bf8e0dc174844b2925634c762bbfd90329387e5da7a1d3fc11db"}}}, "sortedMiddleware": ["/"], "functions": {}}