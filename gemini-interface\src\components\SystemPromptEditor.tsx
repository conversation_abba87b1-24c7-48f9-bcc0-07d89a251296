'use client';

import { useState, useEffect } from 'react';
import { X, Save, Plus, Trash2 } from 'lucide-react';

type SystemPrompt = {
  id: string;
  name: string;
  content: string;
  isDefault: boolean;
};

type SystemPromptEditorProps = {
  isOpen: boolean;
  onClose: () => void;
  onSave: (prompt: Omit<SystemPrompt, 'id'>) => void;
  prompts: SystemPrompt[];
  onSelect: (id: string) => void;
  onDelete: (id: string) => void;
  activePromptId: string | null;
};

export default function SystemPromptEditor({
  isOpen,
  onClose,
  onSave,
  prompts,
  onSelect,
  onDelete,
  activePromptId,
}: SystemPromptEditorProps) {
  const [name, setName] = useState('');
  const [content, setContent] = useState('');
  const [isDefault, setIsDefault] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);

  useEffect(() => {
    if (!isOpen) {
      setName('');
      setContent('');
      setIsDefault(false);
      setEditingId(null);
    }
  }, [isOpen]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!name.trim() || !content.trim()) return;
    
    onSave({
      name: name.trim(),
      content: content.trim(),
      isDefault,
    });
    
    setName('');
    setContent('');
    setIsDefault(false);
    setEditingId(null);
  };

  const handleEdit = (prompt: SystemPrompt) => {
    setName(prompt.name);
    setContent(prompt.content);
    setIsDefault(prompt.isDefault);
    setEditingId(prompt.id);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] flex flex-col">
        <div className="p-4 border-b flex justify-between items-center">
          <h3 className="text-lg font-medium">System Prompts</h3>
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="flex flex-1 overflow-hidden">
          {/* Sidebar with saved prompts */}
          <div className="w-1/3 border-r p-4 overflow-y-auto">
            <div className="mb-4">
              <button
                onClick={() => {
                  setName('');
                  setContent('');
                  setIsDefault(false);
                  setEditingId(null);
                }}
                className="w-full flex items-center justify-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
              >
                <Plus className="w-4 h-4" />
                New Prompt
              </button>
            </div>
            
            <div className="space-y-2">
              {prompts.map((prompt) => (
                <div 
                  key={prompt.id}
                  className={`p-3 rounded-md cursor-pointer text-sm ${
                    activePromptId === prompt.id 
                      ? 'bg-blue-100 border-blue-300' 
                      : 'hover:bg-gray-100 border-transparent'
                  } border`}
                  onClick={() => handleEdit(prompt)}
                >
                  <div className="flex justify-between items-start">
                    <div className="font-medium">{prompt.name}</div>
                    {prompt.isDefault && (
                      <span className="text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full">
                        Default
                      </span>
                    )}
                  </div>
                  <p className="text-gray-600 truncate mt-1">
                    {prompt.content.substring(0, 60)}
                    {prompt.content.length > 60 ? '...' : ''}
                  </p>
                </div>
              ))}
              
              {prompts.length === 0 && (
                <p className="text-sm text-gray-500 text-center py-4">
                  No saved prompts yet
                </p>
              )}
            </div>
          </div>

          {/* Editor */}
          <div className="flex-1 flex flex-col p-4 overflow-hidden">
            <form onSubmit={handleSubmit} className="flex-1 flex flex-col">
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Prompt Name
                </label>
                <input
                  type="text"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder="e.g., Friendly Assistant"
                  className="w-full p-2 border rounded-md focus:ring-blue-500 focus:border-blue-500"
                  required
                />
              </div>
              
              <div className="mb-4 flex-1 flex flex-col">
                <div className="flex justify-between items-center mb-1">
                  <label className="block text-sm font-medium text-gray-700">
                    Prompt Content
                  </label>
                  <span className="text-xs text-gray-500">
                    {content.length} characters
                  </span>
                </div>
                <textarea
                  value={content}
                  onChange={(e) => setContent(e.target.value)}
                  placeholder="You are a helpful assistant..."
                  className="flex-1 w-full p-2 border rounded-md font-mono text-sm focus:ring-blue-500 focus:border-blue-500"
                  rows={10}
                  required
                />
              </div>
              
              <div className="flex items-center justify-between">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={isDefault}
                    onChange={(e) => setIsDefault(e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span className="ml-2 text-sm text-gray-700">
                    Set as default for new conversations
                  </span>
                </label>
                
                <div className="space-x-2">
                  {editingId && (
                    <button
                      type="button"
                      onClick={() => onDelete(editingId)}
                      className="px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md flex items-center gap-1"
                    >
                      <Trash2 className="w-4 h-4" />
                      Delete
                    </button>
                  )}
                  
                  <button
                    type="submit"
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm flex items-center gap-1"
                  >
                    <Save className="w-4 h-4" />
                    {editingId ? 'Update' : 'Save'}
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}
