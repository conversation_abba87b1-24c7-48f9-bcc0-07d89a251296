"""
Onboarding Service Main Application

This module creates and configures the FastAPI application for the Onboarding service.
"""

from fastapi import Fast<PERSON><PERSON>, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import logging
import os
from typing import Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan management."""
    # Startup
    logger.info("Starting Onboarding Service")
    
    # Initialize Onboarding service
    try:
        # TODO: Initialize any required services here
        logger.info("Onboarding service initialized successfully")
        yield
    except Exception as e:
        logger.error(f"Failed to initialize Onboarding service: {str(e)}")
        raise
    finally:
        # Cleanup
        logger.info("Shutting down Onboarding Service")

# Create FastAPI app
app = FastAPI(
    title="MyVillage Onboarding Service",
    description="Onboarding service for MyVillage",
    version="0.1.0",
    lifespan=lifespan
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Import and include routers
from .routes import router as onboarding_router
app.include_router(onboarding_router, prefix="/api/onboarding", tags=["onboarding"])

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "ok", "service": "onboarding"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=8001, reload=True)
