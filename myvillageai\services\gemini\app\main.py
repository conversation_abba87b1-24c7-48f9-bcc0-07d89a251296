"""
Gemini Service Main Application

This module creates and configures the FastAPI application for the Gemini service.
"""

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import logging
import os
from typing import Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan management."""
    # Startup
    logger.info("Starting Gemini Service")
    
    # Initialize Gemini service
    try:
        # TODO: Initialize Gemini client here
        logger.info("Gemini service initialized successfully")
        yield
    except Exception as e:
        logger.error(f"Failed to initialize Gemini service: {str(e)}")
        raise
    finally:
        # Cleanup
        logger.info("Shutting down Gemini Service")

# Create FastAPI app
app = FastAPI(
    title="MyVillage Gemini Service",
    description="Gemini API service for MyVillage",
    version="0.1.0",
    lifespan=lifespan
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Import and include routers
from .routes import router as gemini_router
app.include_router(gemini_router, prefix="/api/gemini", tags=["gemini"])

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "ok", "service": "gemini"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=8002, reload=True)
