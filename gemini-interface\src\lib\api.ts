// API Configuration
const BACKEND_URL = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8000';

// Common headers for API requests
const getHeaders = (sessionId?: string): Record<string, string> => {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };
  
  if (sessionId) {
    // Support both session-id and session_id for compatibility
    headers['session-id'] = sessionId;
    headers['session_id'] = sessionId;
  }
  
  return headers;
};

// Generic API request handler
async function apiRequest<T>(
  endpoint: string,
  method: string = 'GET',
  body?: any,
  sessionId?: string
): Promise<T> {
  try {
    const url = `${BACKEND_URL}${endpoint}`;
    const headers = getHeaders(sessionId);
    
    const response = await fetch(url, {
      method,
      headers,
      body: body ? JSON.stringify(body) : undefined,
      credentials: 'include',
      mode: 'cors',
    });

    const data = await response.json().catch(() => ({}));

    if (!response.ok) {
      throw new Error(data.message || `API request failed with status ${response.status}`);
    }

    return data;
  } catch (error) {
    console.error('API request error:', error);
    throw error;
  }
}

// Chat API Service
export const geminiService = {
  async chat(message: string, sessionId?: string): Promise<any> {
    return apiRequest('/gemini-chat-with-intent', 'POST', { message }, sessionId);
  },
  
  async listModels(): Promise<any> {
    return apiRequest('/api/gemini/models');
  },
};

// Onboarding API Service
export const onboardingService = {
  async getIntent(message: string, sessionId?: string): Promise<any> {
    return apiRequest('/api/onboarding/intent', 'POST', { message }, sessionId);
  },
};

// MCP API Service
export const mcpService = {
  // Add MCP specific API methods here
  async getStatus(sessionId?: string): Promise<any> {
    return apiRequest('/api/mcp/status', 'GET', undefined, sessionId);
  },
};
