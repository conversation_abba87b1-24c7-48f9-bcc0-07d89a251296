import type { <PERSON>ada<PERSON>, Viewport } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { ToolProvider } from "@/contexts/ToolContext";
import { ChatProvider } from "@/contexts/ChatContext";
import { ThemeProvider } from "@/components/theme/ThemeProvider";
import Toaster from "@/components/ui/Toaster";
import { toast } from "@/components/ui/Toaster";
import HtmlWrapper from "@/components/HtmlWrapper";

// Make toast available globally for easier access
type ToastType = 'default' | 'success' | 'error' | 'warning' | 'info';
declare global {
  interface Window {
    toast: typeof toast & {
      (message: string, type?: ToastType, duration?: number): string;
      success: (message: string, duration?: number) => string;
      error: (message: string, duration?: number) => string;
      warning: (message: string, duration?: number) => string;
      info: (message: string, duration?: number) => string;
      remove: (id: string) => void;
    };
  }
}

// Assign to window if running in browser
if (typeof window !== 'undefined') {
  window.toast = toast;
}
import MainNav from "@/components/MainNav";

// Fonts
const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
  display: "swap",
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "My Village AI - Your Community Assistant",
  description: "Connect with My Village AI, your intelligent community assistant designed to help with local information, support, and guidance.",
  keywords: ["AI assistant", "community", "village", "local support", "chatbot"],
  authors: [{ name: "My Village AI Team" }],
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "hsl(var(--primary))" },
    { media: "(prefers-color-scheme: dark)", color: "hsl(var(--primary))" },
    { media: "(prefers-color-scheme: dark)", color: "#9cc954" }
  ],
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      </head>
      <body className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen bg-background text-foreground`}>
        <HtmlWrapper>
          <ChatProvider>
            <ToolProvider>
              <ThemeProvider attribute="class" defaultTheme="light" enableSystem={false}>
                <div className="min-h-screen flex flex-col">
                  <MainNav />
                  <main className="flex-1">
                    {children}
                  </main>
                  <footer className="border-t border-gray-200 py-6">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                      <p className="text-center text-sm text-gray-500">
                        &copy; {new Date().getFullYear()} My Village Project. All rights reserved.
                      </p>
                    </div>
                  </footer>
                </div>
                <Toaster />
              </ThemeProvider>
            </ToolProvider>
          </ChatProvider>
        </HtmlWrapper>
      </body>
    </html>
  );
}
