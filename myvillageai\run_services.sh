#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Store PIDs of background processes
declare -A SERVICE_PIDS

# Function to log messages
log() {
    echo -e "[$(date +'%Y-%m-%d %H:%M:%S')] $1"
}

# Function to display error and exit
error_exit() {
    echo -e "${RED}ERROR: $1${NC}" >&2
    cleanup
    exit 1
}

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to stop all services
cleanup() {
    echo -e "\n${YELLOW}Stopping all services...${NC}"
    
    # Kill each service by its stored PID
    for service in "${!SERVICE_PIDS[@]}"; do
        local pid=${SERVICE_PIDS[$service]}
        if ps -p "$pid" > /dev/null 2>&1; then
            log "Stopping $service (PID: $pid)..."
            kill -TERM "$pid" 2>/dev/null || kill -9 "$pid" 2>/dev/null
            wait "$pid" 2>/dev/null
            log "${GREEN}Stopped $service${NC}"
        fi
    done
    
    echo -e "\n${GREEN}All services have been stopped. Logs are available in the respective service directories.${NC}"
    exit 0
}

# Function to start a service
start_service() {
    local service_name="$1"
    local port="$2"
    local working_dir="$3"
    
    log "${GREEN}Starting ${service_name}...${NC}"
    
    # Check if working directory exists
    if [ ! -d "$working_dir" ]; then
        error_exit "Directory '$working_dir' does not exist for $service_name"
    fi
    
    # Start the service in the background
    (cd "$working_dir" && uvicorn app.main:app --port "$port" --reload > "${service_name// /_}.log" 2>&1) &
    
    # Store the PID
    SERVICE_PIDS["$service_name"]=$!
    
    log "${GREEN}Started $service_name with PID ${SERVICE_PIDS[$service_name]} on port $port${NC}"
    sleep 2  # Small delay to let the service start
}

# Get the project root directory
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Check if uvicorn is installed
if ! command_exists uvicorn; then
    error_exit "uvicorn is not installed. Please install it with: pip install uvicorn"
fi

# Start all services
start_service "API Gateway" 8000 "$PROJECT_ROOT/api"
start_service "Onboarding Service" 8001 "$PROJECT_ROOT/services/onboarding"
start_service "Gemini Service" 8002 "$PROJECT_ROOT/services/gemini"
start_service "MCP Service" 8003 "$PROJECT_ROOT/mcp"

# Display service information
echo -e "\n${GREEN}All services have been started.${NC}"
echo -e "\n${YELLOW}Service Information:${NC}"
echo -e "${YELLOW}-------------------${NC}"
echo -e "${GREEN}API Gateway:    http://localhost:8000"
echo -e "Onboarding API: http://localhost:8001"
echo -e "Gemini API:     http://localhost:8002"
echo -e "MCP API:        http://localhost:8003${NC}"

# Set up trap to catch Ctrl+C
trap cleanup SIGINT

echo -e "\n${YELLOW}Press Ctrl+C to stop all services...${NC}"

# Keep the script running
while true; do
    sleep 1
done