'use client';

import { useState, useEffect } from 'react';

export type ParameterDefinition = {
  type: string;
  description: string;
  required?: boolean;
  enum?: string[];
  default?: any;
  min?: number;
  max?: number;
  pattern?: string;
};

export type ParameterValue = string | number | boolean | null | undefined;

type ToolParameters = Record<string, ParameterValue>;

interface ToolParameterFormProps {
  parameters: Record<string, ParameterDefinition>;
  values: ToolParameters;
  onChange: (values: ToolParameters) => void;
  onSubmit: (values: ToolParameters) => void;
  isSubmitting?: boolean;
  submitLabel?: string;
}

export default function ToolParameterForm({
  parameters,
  values,
  onChange,
  onSubmit,
  isSubmitting = false,
  submitLabel = 'Submit'
}: ToolParameterFormProps) {
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  useEffect(() => {
    // Initialize form with default values
    const initialValues = { ...values };
    let hasChanges = false;

    Object.entries(parameters).forEach(([name, def]) => {
      if (initialValues[name] === undefined && def.default !== undefined) {
        initialValues[name] = def.default;
        hasChanges = true;
      }
    });

    if (hasChanges) {
      onChange(initialValues);
    }
  }, [parameters]);

  const validate = (name: string, value: ParameterValue, def: ParameterDefinition) => {
    const newErrors = { ...errors };

    if (def.required && (value === '' || value === null || value === undefined)) {
      newErrors[name] = 'This field is required';
    } else if (def.type === 'number' && value !== '' && value !== null) {
      const numValue = Number(value);
      if (isNaN(numValue)) {
        newErrors[name] = 'Must be a valid number';
      } else {
        if (def.min !== undefined && numValue < def.min) {
          newErrors[name] = `Must be at least ${def.min}`;
        }
        if (def.max !== undefined && numValue > def.max) {
          newErrors[name] = `Must be at most ${def.max}`;
        }
      }
    } else if (def.pattern && value && typeof value === 'string') {
      const regex = new RegExp(def.pattern);
      if (!regex.test(value)) {
        newErrors[name] = 'Invalid format';
      }
    } else {
      delete newErrors[name];
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (name: string, value: ParameterValue, def: ParameterDefinition) => {
    // Convert empty strings to null for required fields
    const newValue = value === '' && def.required ? null : value;
    
    const newValues = {
      ...values,
      [name]: newValue,
    };

    onChange(newValues);
    validate(name, newValue, def);
  };

  const handleBlur = (name: string, value: ParameterValue, def: ParameterDefinition) => {
    setTouched(prev => ({ ...prev, [name]: true }));
    validate(name, value, def);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate all fields
    let isValid = true;
    const newTouched: Record<string, boolean> = {};
    
    Object.entries(parameters).forEach(([name, def]) => {
      newTouched[name] = true;
      if (!validate(name, values[name], def)) {
        isValid = false;
      }
    });
    
    setTouched(newTouched);
    
    if (isValid) {
      onSubmit(values);
    }
  };

  const renderInput = (name: string, def: ParameterDefinition) => {
    const value = values[name] ?? '';
    const isTouched = touched[name];
    const error = errors[name];
    const showError = isTouched && error;
    const inputId = `param-${name}`;
    
    // Convert value to string for input elements
    const inputValue = value === null || value === undefined ? '' : String(value);

    const commonInputProps = {
      id: inputId,
      name,
      value: inputValue,
      onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
        let newValue: ParameterValue = e.target.value;
        
        if (def.type === 'number' || def.type === 'integer') {
          newValue = newValue === '' ? null : Number(newValue);
        } else if (def.type === 'boolean') {
          newValue = (e.target as HTMLInputElement).checked;
        }
        
        handleChange(name, newValue, def);
      },
      onBlur: () => handleBlur(name, value, def),
      className: `block w-full rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
        showError
          ? 'border-red-300 text-red-900 placeholder-red-300'
          : 'border-gray-300'
      }`,
      disabled: isSubmitting,
    };

    const label = (
      <label
        htmlFor={inputId}
        className="block text-sm font-medium text-gray-700 mb-1"
      >
        {name}
        {def.required && <span className="text-red-500 ml-1">*</span>}
      </label>
    );

    const description = def.description && (
      <p className="mt-1 text-xs text-gray-500">{def.description}</p>
    );

    const errorMessage = showError && (
      <p className="mt-1 text-sm text-red-600">{error}</p>
    );

    // Handle enum types (dropdown)
    if (def.enum) {
      return (
        <div className="mb-4">
          {label}
          <select
            {...commonInputProps}
            className={`${commonInputProps.className} mt-1`}
            value={inputValue}
          >
            {!def.required && <option value="">-- Select an option --</option>}
            {def.enum.map((option) => (
              <option key={option} value={option}>
                {option}
              </option>
            ))}
          </select>
          {description}
          {errorMessage}
        </div>
      );
    }

    // Handle boolean type (checkbox)
    if (def.type === 'boolean') {
      return (
        <div className="mb-4">
          <div className="flex items-center">
            <input
              id={inputId}
              name={name}
              type="checkbox"
              checked={!!value}
              onChange={(e) => handleChange(name, e.target.checked, def)}
              onBlur={() => handleBlur(name, value, def)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              disabled={isSubmitting}
            />
            <label
              htmlFor={inputId}
              className="ml-2 block text-sm text-gray-700"
            >
              {name}
              {def.required && <span className="text-red-500 ml-1">*</span>}
            </label>
          </div>
          {description}
          {errorMessage}
        </div>
      );
    }

    // Handle textarea for longer text
    if (def.type === 'string' && (def.description?.length ?? 0) > 50) {
      return (
        <div className="mb-4">
          {label}
          <textarea
            {...commonInputProps}
            rows={4}
            className={`${commonInputProps.className} mt-1`}
            value={inputValue}
          />
          {description}
          {errorMessage}
        </div>
      );
    }

    // Handle number input
    if (def.type === 'number' || def.type === 'integer') {
      return (
        <div className="mb-4">
          {label}
          <input
            {...commonInputProps}
            type="number"
            step={def.type === 'integer' ? '1' : 'any'}
            min={def.min}
            max={def.max}
            className={`${commonInputProps.className} mt-1`}
          />
          {description}
          {errorMessage}
        </div>
      );
    }

    // Default text input
    return (
      <div className="mb-4">
        {label}
        <input
          {...commonInputProps}
          type={def.type === 'password' ? 'password' : 'text'}
          placeholder={def.description}
          className={`${commonInputProps.className} mt-1`}
          value={inputValue}
        />
        {description}
        {errorMessage}
      </div>
    );
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {Object.entries(parameters).map(([name, def]) => (
        <div key={name}>
          {renderInput(name, def)}
        </div>
      ))}
      
      <div className="pt-2">
        <button
          type="submit"
          disabled={isSubmitting || Object.keys(errors).length > 0}
          className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isSubmitting ? 'Submitting...' : submitLabel}
        </button>
      </div>
    </form>
  );
}
