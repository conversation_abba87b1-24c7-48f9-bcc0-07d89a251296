'use client';

import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import { useChatContext } from '@/contexts/ChatContext';
import ModelSelector from '@/components/ModelSelector';
import SystemPromptButton from '@/components/SystemPromptButton';
import { Loader2 } from 'lucide-react';

const ChatPage = () => {
  const { messages, addMessage, isModelLoading } = useChatContext();
  const [input, setInput] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || isSubmitting) return;
    
    const userMessage = input;
    setInput('');
    setIsSubmitting(true);
    
    try {
      // Add user message
      await addMessage({
        role: 'user',
        content: userMessage,
      });
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-3xl mx-auto p-4 relative min-h-screen">
      <SystemPromptButton />
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Chat with AI</h1>
        <ModelSelector />
      </div>
      
      <div className="space-y-4 mb-6">
        {messages.map((message) => (
          <div 
            key={message.id} 
            className={`p-4 rounded-lg ${
              message.role === 'user' 
                ? 'bg-blue-50 text-blue-900 ml-8' 
                : 'bg-gray-50 text-gray-900 mr-8'
            }`}
          >
            <div className="font-medium mb-1">
              {message.role === 'user' ? 'You' : 'AI'}
            </div>
            <div>{message.content}</div>
          </div>
        ))}
      </div>
      
      <form onSubmit={handleSubmit} className="flex gap-2 fixed bottom-0 left-0 right-0 bg-white p-4 border-t">
        <div className="flex-1 max-w-3xl mx-auto flex gap-2">
          <input
            type="text"
            value={input}
            disabled={isSubmitting || isModelLoading}
            onChange={(e) => setInput(e.target.value)}
            placeholder="Type your message..."
            className="flex-1 p-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleSubmit(e);
              }
            }}
          />
          <button 
            type="submit"
            disabled={!input.trim() || isSubmitting || isModelLoading}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 flex items-center gap-2 min-w-[80px] justify-center"
          >
            {(isSubmitting || isModelLoading) ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>Sending...</span>
              </>
            ) : 'Send'}
          </button>
        </div>
      </form>
      
      {/* Add some bottom padding to prevent content from being hidden behind the button */}
      <div className="h-20"></div>
    </div>
  );
}
