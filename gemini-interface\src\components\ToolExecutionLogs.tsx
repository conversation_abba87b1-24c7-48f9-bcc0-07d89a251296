'use client';

import { useToolContext } from '@/contexts/ToolContext';
import { formatDistanceToNow } from 'date-fns';

export default function ToolExecutionLogs() {
  const { toolCalls } = useToolContext();

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'bg-green-100 text-green-800';
      case 'error':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (toolCalls.length === 0) {
    return (
      <div className="p-4 text-center text-gray-500">
        No tool executions yet. Use a tool to see logs here.
      </div>
    );
  }

  return (
    <div className="space-y-2 max-h-96 overflow-y-auto p-2">
      {toolCalls.map((tool) => (
        <div 
          key={tool.id} 
          className="border rounded-lg p-3 hover:shadow-md transition-shadow bg-white"
        >
          <div className="flex justify-between items-start">
            <div className="flex-1 min-w-0">
              <h4 className="text-sm font-medium text-gray-900 truncate">
                {tool.toolName}
              </h4>
              <div className="mt-1 flex items-center text-xs text-gray-500">
                <span>
                  {formatDistanceToNow(tool.timestamp, { addSuffix: true })}
                </span>
                {tool.duration && (
                  <span className="mx-2">•</span>
                )}
                {tool.duration && (
                  <span>{tool.duration}ms</span>
                )}
              </div>
            </div>
            <span 
              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(tool.status)}`}
            >
              {tool.status}
            </span>
          </div>
          
          {tool.error && (
            <div className="mt-2 text-xs text-red-600 bg-red-50 p-2 rounded">
              {tool.error}
            </div>
          )}
        </div>
      ))}
    </div>
  );
}
