'use client';

import { useState } from 'react';
import ToolLauncher from './ToolLauncher';
import ToolExecutionLogs from './ToolExecutionLogs';

type Tool = {
  name: string;
  description: string;
  parameters?: Record<string, any>;
};

export default function ToolDiscovery() {
  type TabType = 'list' | 'launch' | 'logs';
  const [activeTab, setActiveTab] = useState<TabType>('list');
  
  // Mock tools data - in a real app, this would come from an API
  const tools: Tool[] = [
    {
      name: 'browser_navigate',
      description: 'Navigate to a URL in the browser',
      parameters: {
        url: { 
          type: 'string', 
          description: 'The URL to navigate to',
          required: true
        }
      }
    },
    {
      name: 'browser_click',
      description: 'Click on an element on the page',
      parameters: {
        selector: { 
          type: 'string', 
          description: 'CSS selector for the element to click',
          required: true
        }
      }
    },
    {
      name: 'browser_type',
      description: 'Type text into an input field',
      parameters: {
        selector: { 
          type: 'string', 
          description: 'CSS selector for the input field',
          required: true
        },
        text: { 
          type: 'string', 
          description: 'Text to type',
          required: true
        },
        submit: { 
          type: 'boolean', 
          description: 'Whether to submit the form after typing',
          required: false
        }
      }
    }
  ];

  return (
    <div className="p-4">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">MCP Tools</h2>
      </div>

      {activeTab === 'launch' && <ToolLauncher />}
      
      {activeTab === 'logs' && (
        <div className="mt-4">
          <h2 className="text-lg font-semibold mb-4">Tool Execution Logs</h2>
          <ToolExecutionLogs />
        </div>
      )}
      
      {activeTab === 'list' && (
        <div className="space-y-4">
          <div className="bg-white shadow overflow-hidden sm:rounded-md">
            <ul className="divide-y divide-gray-200">
              {tools.map((tool, index) => (
                <li key={index} className="p-4 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">{tool.name}</h3>
                      <p className="text-sm text-gray-500">{tool.description}</p>
                      {tool.parameters && (
                        <div className="mt-2">
                          <h4 className="text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Parameters
                          </h4>
                          <ul className="mt-1 space-y-1">
                            {Object.entries(tool.parameters).map(([paramName, paramDef]) => (
                              <li key={paramName} className="text-sm text-gray-700">
                                <span className="font-mono bg-gray-100 px-1 rounded">{paramName}</span>
                                <span className="text-gray-500 text-xs ml-2">
                                  {paramDef.type}
                                  {paramDef.required && <span className="text-red-500 ml-1">*</span>}
                                </span>
                                {paramDef.description && (
                                  <p className="text-xs text-gray-500 mt-0.5 ml-4">{paramDef.description}</p>
                                )}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                    <button
                      onClick={() => {
                        setActiveTab('launch');
                        // In a real app, you might want to pre-select the tool in the launcher
                      }}
                      className="ml-4 inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      Launch
                    </button>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        </div>
      )}
    </div>
  );
}
