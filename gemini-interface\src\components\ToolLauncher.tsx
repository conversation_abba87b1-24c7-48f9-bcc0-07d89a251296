'use client';

import { useState } from 'react';
import { useToolContext } from '@/contexts/ToolContext';
import ToolParameterForm, { type ParameterValue, type ParameterDefinition } from './ToolParameterForm';

type ToolParameters = Record<string, ParameterValue>;

type ToolDefinition = {
  name: string;
  description: string;
  parameters: {
    [key: string]: ParameterDefinition;
  };
};

export default function ToolLauncher() {
  const [selectedTool, setSelectedTool] = useState<string>('');
  const [parameters, setParameters] = useState<ToolParameters>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const { addToolCall, updateToolCall } = useToolContext();

  // Mock data - in a real app, this would come from your API
  const availableTools: ToolDefinition[] = [
    {
      name: 'browser_navigate',
      description: 'Navigate to a URL in the browser',
      parameters: {
        url: {
          type: 'string',
          description: 'The URL to navigate to',
          required: true
        }
      }
    },
    {
      name: 'browser_click',
      description: 'Click on an element on the page',
      parameters: {
        selector: {
          type: 'string',
          description: 'CSS selector for the element to click',
          required: true
        }
      }
    },
    {
      name: 'browser_type',
      description: 'Type text into an input field',
      parameters: {
        selector: {
          type: 'string',
          description: 'CSS selector for the input field',
          required: true
        },
        text: {
          type: 'string',
          description: 'Text to type',
          required: true
        },
        submit: {
          type: 'boolean',
          description: 'Whether to submit the form after typing',
          required: false
        }
      }
    }
  ];

  const handleToolSelect = (toolName: string) => {
    setSelectedTool(toolName);
    setParameters({});
    setResult(null);
    setError(null);
  };

  // No longer needed as ToolParameterForm handles the state

  const handleSubmit = async (formValues: Record<string, any>) => {
    if (!selectedTool) return;

    setIsSubmitting(true);
    setError(null);
    
    // Get the selected tool definition
    const tool = availableTools.find(t => t.name === selectedTool);
    if (!tool) {
      setError('Selected tool not found');
      setIsSubmitting(false);
      return;
    }

    // Create a tool call
    const toolCallId = addToolCall(selectedTool);

    try {
      // In a real app, this would call your API to execute the tool
      console.log(`Executing ${selectedTool} with params:`, formValues);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // For demo purposes, just echo back the parameters
      const mockResult = {
        tool: selectedTool,
        parameters: formValues,
        timestamp: new Date().toISOString(),
        success: true
      };
      
      setResult(mockResult);
      updateToolCall(toolCallId, { 
        status: 'success'
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to execute tool';
      setError(errorMessage);
      updateToolCall(toolCallId, { 
        status: 'error',
        error: errorMessage 
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const selectedToolDef = availableTools.find(t => t.name === selectedTool);

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-lg font-medium mb-2">Available Tools</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {availableTools.map((tool) => (
            <button
              key={tool.name}
              onClick={() => handleToolSelect(tool.name)}
              className={`p-4 border rounded-lg text-left transition-colors ${
                selectedTool === tool.name
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-blue-300 hover:bg-gray-50'
              }`}
            >
              <h3 className="font-medium text-gray-900">{tool.name}</h3>
              <p className="text-sm text-gray-500 mt-1">{tool.description}</p>
            </button>
          ))}
        </div>
      </div>

      {selectedToolDef && (
        <div className="mt-6 p-4 border rounded-lg bg-white">
          <h3 className="text-lg font-medium mb-4">
            {selectedToolDef.name}
            <span className="ml-2 text-sm font-normal text-gray-500">
              {selectedToolDef.description}
            </span>
          </h3>
          
          <ToolParameterForm
            parameters={selectedToolDef.parameters}
            values={parameters}
            onChange={(values) => setParameters(values)}
            onSubmit={handleSubmit}
            isSubmitting={isSubmitting}
            submitLabel={isSubmitting ? 'Executing...' : 'Execute Tool'}
          />

          {error && (
            <div className="mt-4 p-3 text-sm text-red-700 bg-red-100 rounded-md">
              {error}
            </div>
          )}

          {result && (
            <div className="mt-4 p-3 text-sm bg-gray-50 rounded-md overflow-auto">
              <h4 className="font-medium mb-2">Result:</h4>
              <pre className="text-xs">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
