"""
API Gateway Main Application

This module creates and configures the main FastAPI application that acts as an API Gateway,
routing requests to the appropriate microservices.
"""

import httpx
from fastapi import FastAPI, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import logging
import os
from typing import Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Service URLs (to be configured via environment variables)
SERVICE_URLS = {
    "onboarding": os.getenv("ONBOARDING_SERVICE_URL", "http://localhost:8001"),
    "gemini": os.getenv("GEMINI_SERVICE_URL", "http://localhost:8002"),
    "mcp": os.getenv("MCP_SERVICE_URL", "http://localhost:8003"),
}

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan management."""
    # Startup
    logger.info("Starting MyVillage API Gateway")
    logger.info(f"Service URLs: {SERVICE_URLS}")
    
    # Create async HTTP client
    async with httpx.AsyncClient() as client:
        app.state.http_client = client
        yield
    
    # Cleanup
    logger.info("Shutting down MyVillage API Gateway")

# Create FastAPI app
app = FastAPI(
    title="MyVillage API Gateway",
    description="API Gateway for MyVillage services",
    version="0.1.0",
    lifespan=lifespan
)

# Configure CORS
# Update the CORS middleware in the API Gateway
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # Add your frontend URL
    allow_credentials=True,
    allow_methods=["*"],  # Allow all methods (GET, POST, etc.)
    allow_headers=["*"],  # Allow all headers
)

# Service routing configuration
SERVICE_ROUTES = {
    "/api/onboarding": "onboarding",
    "/api/gemini": "gemini",
    "/api/mcp": "mcp",
    "/onboarding": "onboarding",  # For backward compatibility
    "/gemini": "gemini",          # For backward compatibility
    "/mcp": "mcp",               # For backward compatibility
    "/gemini-chat-with-intent": "gemini"  # Direct route for the chat endpoint
}

@app.middleware("http")
async def reverse_proxy(request: Request, call_next):
    """
    Reverse proxy middleware to route requests to appropriate services.
    
    This will forward requests to the corresponding microservice based on the path.
    Example: /api/onboarding/* -> Onboarding Service
    """
    path = request.url.path
    
    # Find matching service
    service_name = None
    matched_prefix = ""
    for prefix, name in SERVICE_ROUTES.items():
        if path.startswith(prefix):
            service_name = name
            matched_prefix = prefix
            break
    
    if not service_name:
        return await call_next(request)
    
    # Forward request to the appropriate service
    service_url = SERVICE_URLS.get(service_name)
    if not service_url:
        raise HTTPException(status_code=502, detail=f"Service {service_name} is not available")
    
    # Rewrite the path if needed
    if matched_prefix == "/gemini-chat-with-intent":
        # For the chat endpoint, we want to keep the full path
        target_url = f"{service_url}{path}"
    else:
        # For other endpoints, strip the prefix
        target_url = f"{service_url}{path[len(matched_prefix):] or '/'}"
    
    # Forward the request
    client: httpx.AsyncClient = request.app.state.http_client
    
    # Get request body if present
    body = None
    if request.method in ["POST", "PUT", "PATCH"]:
        body = await request.body()
    
    # Forward headers (excluding some that should be handled by the gateway)
    headers = dict(request.headers)
    headers.pop("host", None)
    headers.pop("content-length", None)
    
    try:
        # Make the request to the target service
        response = await client.request(
            method=request.method,
            url=target_url,
            params=request.query_params,
            headers=headers,
            content=body,
            follow_redirects=False,
            timeout=30.0
        )
        
        # Return the response from the service
        return response
        
    except httpx.RequestError as e:
        logger.error(f"Error forwarding request to {service_name}: {str(e)}")
        raise HTTPException(
            status_code=502,
            detail=f"Error connecting to {service_name} service"
        )

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "ok", "service": "api-gateway"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)
