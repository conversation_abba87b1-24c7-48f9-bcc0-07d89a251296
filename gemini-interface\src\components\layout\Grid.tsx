import * as React from 'react';
import { cn } from '@/lib/utils';

interface GridProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * Number of columns (1-12)
   * @default 12
   */
  cols?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;
  /**
   * Gap between grid items (in rem)
   * @default 1
   */
  gap?: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 8 | 10 | 12 | 16 | 20 | 24;
  /**
   * Responsive columns (mobile, tablet, desktop)
   * @default { sm: 1, md: 2, lg: 4 }
   */
  responsive?: {
    sm?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;
    md?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;
    lg?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;
    xl?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;
    '2xl'?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;
  };
}

export const Grid = React.forwardRef<HTMLDivElement, GridProps>(
  (
    {
      className,
      children,
      cols = 12,
      gap = 1,
      responsive = { sm: 1, md: 2, lg: 4 },
      ...props
    },
    ref
  ) => {
    const responsiveClasses = [
      responsive.sm && `grid-cols-${responsive.sm}`,
      responsive.md && `md:grid-cols-${responsive.md}`,
      responsive.lg && `lg:grid-cols-${responsive.lg}`,
      responsive.xl && `xl:grid-cols-${responsive.xl}`,
      responsive['2xl'] && `2xl:grid-cols-${responsive['2xl']}`,
    ].filter(Boolean);

    return (
      <div
        ref={ref}
        className={cn(
          'grid',
          `grid-cols-${cols}`,
          `gap-${gap}`,
          responsiveClasses,
          className
        )}
        {...props}
      >
        {children}
      </div>
    );
  }
);

Grid.displayName = 'Grid';

interface GridItemProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * Number of columns the item should span (1-12)
   * @default 1
   */
  span?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;
  /**
   * Responsive column spans
   */
  responsive?: {
    sm?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;
    md?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;
    lg?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;
    xl?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;
    '2xl'?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;
  };
  /**
   * Column start position
   */
  start?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 | 13;
  /**
   * Column end position
   */
  end?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 | 13;
}

export const GridItem = React.forwardRef<HTMLDivElement, GridItemProps>(
  (
    {
      className,
      children,
      span = 1,
      responsive,
      start,
      end,
      ...props
    },
    ref
  ) => {
    const responsiveClasses = [
      responsive?.sm && `col-span-${responsive.sm}`,
      responsive?.md && `md:col-span-${responsive.md}`,
      responsive?.lg && `lg:col-span-${responsive.lg}`,
      responsive?.xl && `xl:col-span-${responsive.xl}`,
      responsive?.['2xl'] && `2xl:col-span-${responsive['2xl']}`,
      start && `col-start-${start}`,
      end && `col-end-${end}`,
    ].filter(Boolean);

    return (
      <div
        ref={ref}
        className={cn(
          `col-span-${span}`,
          responsiveClasses,
          className
        )}
        {...props}
      >
        {children}
      </div>
    );
  }
);

GridItem.displayName = 'GridItem';

// Example usage:
/*
<Grid cols={12} gap={4} responsive={{ sm: 1, md: 2, lg: 3 }}>
  <GridItem span={12} className="bg-blue-100 p-4">Header</GridItem>
  <GridItem span={4} className="bg-green-100 p-4">Sidebar</GridItem>
  <GridItem span={8} className="bg-yellow-100 p-4">Main Content</GridItem>
  <GridItem span={12} className="bg-red-100 p-4">Footer</GridItem>
</Grid>
*/
