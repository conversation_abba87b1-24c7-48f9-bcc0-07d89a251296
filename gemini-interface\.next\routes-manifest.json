{"version": 3, "caseSensitive": false, "basePath": "", "rewrites": {"beforeFiles": [], "afterFiles": [{"source": "/api/chat", "destination": "http://localhost:8000/gemini-chat-with-intent", "regex": "^\\/api\\/chat(?:\\/)?$", "check": true}, {"source": "/api/:path*", "destination": "http://localhost:8000/:path*", "regex": "^\\/api(?:\\/((?:[^\\/]+?)(?:\\/(?:[^\\/]+?))*))?(?:\\/)?$", "check": true}], "fallback": []}, "redirects": [{"source": "/:path+/", "destination": "/:path+", "permanent": true, "internal": true, "regex": "^(?:\\/((?:[^\\/]+?)(?:\\/(?:[^\\/]+?))*))\\/$"}], "headers": [{"source": "/api/:path*", "headers": [{"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET,POST,PUT,DELETE,OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "X-Requested-With, Content-Type, Accept, session-id, session_id"}], "regex": "^\\/api(?:\\/((?:[^\\/]+?)(?:\\/(?:[^\\/]+?))*))?(?:\\/)?$"}]}