'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
// import { Home, BookOpen, Users, Brain, MessageSquare, User } from 'lucide-react';

// const navigation = [
//   { name: 'Home', href: '/', icon: Home },
//   { name: 'Learn', href: '/learn', icon: BookOpen },
//   { name: 'Community', href: '/community', icon: Users },
//   { name: 'AI Assistant', href: '/chat', icon: Brain },
//   { name: 'Messages', href: '/messages', icon: MessageSquare },
//   { name: 'Profile', href: '/profile', icon: User },
// ];

export default function MainNav() {
  const pathname = usePathname();

  return (
    <nav className="flex flex-col sm:flex-row justify-between items-center p-4 bg-white border-b border-gray-200">
      <div className="flex items-center mb-4 sm:mb-0">
        <Link href="/" className="flex items-center">
          <span className="text-xl font-bold text-blue-600">MyVillageOS</span>
        </Link>
      </div>
      
      {/* <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
        {navigation.map((item) => {
          const isActive = pathname === item.href;
          return (
            <Link
              key={item.name}
              href={item.href}
              className={`flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                isActive
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              }`}
            >
              <item.icon className="w-5 h-5 mr-2" />
              <span>{item.name}</span>
            </Link>
          );
        })}
      </div> */}
      
      <div className="mt-4 sm:mt-0">
        <Link 
          href="/about" 
          className="text-sm font-medium text-gray-600 hover:text-gray-900"
        >
          About MYVillageOS
        </Link>
      </div>
    </nav>
  );
}
