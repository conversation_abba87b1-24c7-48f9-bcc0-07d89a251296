'use client';

import { useState, useEffect } from 'react';
import { SlidersHorizontal, X, Check, RefreshCw } from 'lucide-react';
import { useChatContext } from '@/contexts/ChatContext';

type Message = {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  includeInContext: boolean;
};

type ContextManagerProps = {
  maxTokens?: number;
};

export default function ContextManager({
  maxTokens = 4000,
}: ContextManagerProps) {
  const { 
    messages, 
    updateMessage, 
    clearMessages, 
    tokenCount 
  } = useChatContext();
  const [isOpen, setIsOpen] = useState(false);
  const [selectedMessageId, setSelectedMessageId] = useState<string | null>(null);

  const toggleIncludeMessage = (id: string) => {
    const message = messages.find(m => m.id === id);
    if (message) {
      updateMessage(id, { includeInContext: !message.includeInContext });
    }
  };

  const tokenPercentage = Math.min(100, (tokenCount / maxTokens) * 100);
  const tokenStatusColor = 
    tokenPercentage > 90 ? 'bg-red-500' :
    tokenPercentage > 70 ? 'bg-yellow-500' : 'bg-blue-500';

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="p-3 bg-white rounded-full shadow-lg hover:bg-gray-100 transition-colors"
        aria-label="Manage context"
      >
        <SlidersHorizontal className="w-5 h-5 text-gray-700" />
      </button>

      {isOpen && (
        <div className="absolute bottom-full right-0 mb-2 w-80 bg-white rounded-lg shadow-xl overflow-hidden border border-gray-200">
          <div className="p-4 border-b border-gray-200 flex justify-between items-center">
            <h3 className="font-medium">Context Manager</h3>
            <button 
              onClick={() => setIsOpen(false)}
              className="text-gray-500 hover:text-gray-700"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          <div className="p-4 border-b border-gray-200">
            <div className="flex justify-between text-sm mb-1">
              <span>Context Usage</span>
              <span>{tokenCount} / {maxTokens} tokens</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div 
                className={`h-2.5 rounded-full ${tokenStatusColor}`}
                style={{ width: `${tokenPercentage}%` }}
              />
            </div>
          </div>

          <div className="max-h-64 overflow-y-auto">
            {messages.map((message) => (
              <div 
                key={message.id}
                className={`p-3 border-b border-gray-100 hover:bg-gray-50 cursor-pointer ${
                  selectedMessageId === message.id ? 'bg-blue-50' : ''
                }`}
                onClick={() => setSelectedMessageId(
                  selectedMessageId === message.id ? null : message.id
                )}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center text-sm mb-1">
                      <span className={`font-medium ${
                        message.role === 'user' ? 'text-blue-600' : 
                        message.role === 'assistant' ? 'text-green-600' : 'text-purple-600'
                      }`}>
                        {message.role.toUpperCase()}
                      </span>
                      <span className="mx-2 text-gray-400">•</span>
                      <span className="text-xs text-gray-500">
                        {message.timestamp.toLocaleTimeString()}
                      </span>
                    </div>
                    <p className="text-sm text-gray-700 truncate">
                      {message.content.substring(0, 100)}
                      {message.content.length > 100 ? '...' : ''}
                    </p>
                  </div>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleIncludeMessage(message.id);
                    }}
                    className={`ml-2 p-1 rounded-full ${
                      message.includeInContext 
                        ? 'text-green-500 hover:bg-green-50' 
                        : 'text-gray-400 hover:bg-gray-100'
                    }`}
                  >
                    {message.includeInContext ? (
                      <Check className="w-4 h-4" />
                    ) : (
                      <X className="w-4 h-4" />
                    )}
                  </button>
                </div>
              </div>
            ))}
          </div>

          <div className="p-3 bg-gray-50 flex justify-between">
            <button
              onClick={clearMessages}
              className="px-3 py-1.5 text-sm text-red-600 hover:bg-red-50 rounded-md flex items-center"
            >
              <RefreshCw className="w-4 h-4 mr-1.5" />
              Clear All
            </button>
            <div className="text-xs text-gray-500">
              {messages.filter(m => m.includeInContext).length} / {messages.length} messages included
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
