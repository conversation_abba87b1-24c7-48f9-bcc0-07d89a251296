'use client';

import { GraduationCap, Users, BrainCircuit, BookOpen, HeartHandshake, Globe } from 'lucide-react';

export default function AboutVillageOS() {
  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">Welcome to VillageOS</h1>
        <p className="text-xl text-gray-600">
          Where learning is a community journey, powered by collective intelligence
        </p>
      </div>

      <div className="bg-white rounded-xl shadow-md p-8 mb-8">
        <h2 className="text-2xl font-semibold text-gray-800 mb-6">Our Vision</h2>
        <p className="text-gray-700 mb-6">
          VillageOS is built on the belief that it truly takes a village to raise a learner. 
          We're reimagining education by combining community wisdom, cultural context, and 
          cutting-edge AI to create a learning experience that's personal, connected, and inspiring.
        </p>
        
        <div className="grid md:grid-cols-2 gap-8 mt-10">
          <div className="bg-blue-50 p-6 rounded-lg">
            <div className="flex items-center mb-4">
              <GraduationCap className="w-8 h-8 text-blue-600 mr-3" />
              <h3 className="text-xl font-semibold">Student-Centered Learning</h3>
            </div>
            <p className="text-gray-700">
              Personalized learning paths that adapt to each student's needs, interests, and pace, 
              while keeping them connected to their community.
            </p>
          </div>

          <div className="bg-green-50 p-6 rounded-lg">
            <div className="flex items-center mb-4">
              <Users className="w-8 h-8 text-green-600 mr-3" />
              <h3 className="text-xl font-semibold">Community Wisdom</h3>
            </div>
            <p className="text-gray-700">
              Tap into the collective knowledge of your community, where everyone can 
              be both a teacher and a learner.
            </p>
          </div>

          <div className="bg-purple-50 p-6 rounded-lg">
            <div className="flex items-center mb-4">
              <BrainCircuit className="w-8 h-8 text-purple-600 mr-3" />
              <h3 className="text-xl font-semibold">AI-Enhanced</h3>
            </div>
            <p className="text-gray-700">
              Our AI helps connect learners with the right resources, mentors, and 
              peers at the right time.
            </p>
          </div>

          <div className="bg-amber-50 p-6 rounded-lg">
            <div className="flex items-center mb-4">
              <BookOpen className="w-8 h-8 text-amber-600 mr-3" />
              <h3 className="text-xl font-semibold">Cultural Context</h3>
            </div>
            <p className="text-gray-700">
              Learning that respects and incorporates cultural heritage and local knowledge, 
              making education more relevant and meaningful.
            </p>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-xl shadow-md p-8">
        <h2 className="text-2xl font-semibold text-gray-800 mb-6">How It Works</h2>
        <div className="space-y-8">
          <div className="flex flex-col md:flex-row gap-6">
            <div className="bg-blue-100 w-12 h-12 rounded-full flex items-center justify-center flex-shrink-0">
              <span className="text-blue-600 font-bold text-xl">1</span>
            </div>
            <div>
              <h3 className="text-xl font-semibold mb-2">Connect with Your Village</h3>
              <p className="text-gray-700">
                Join or create learning communities with family members, mentors, and peers who share 
                your educational goals and cultural background.
              </p>
            </div>
          </div>

          <div className="flex flex-col md:flex-row gap-6">
            <div className="bg-green-100 w-12 h-12 rounded-full flex items-center justify-center flex-shrink-0">
              <span className="text-green-600 font-bold text-xl">2</span>
            </div>
            <div>
              <h3 className="text-xl font-semibold mb-2">Discover Learning Paths</h3>
              <p className="text-gray-700">
                Explore personalized learning journeys that combine academic subjects with 
                cultural knowledge and practical skills.
              </p>
            </div>
          </div>

          <div className="flex flex-col md:flex-row gap-6">
            <div className="bg-purple-100 w-12 h-12 rounded-full flex items-center justify-center flex-shrink-0">
              <span className="text-purple-600 font-bold text-xl">3</span>
            </div>
            <div>
              <h3 className="text-xl font-semibold mb-2">Collaborate and Grow</h3>
              <p className="text-gray-700">
                Work on projects, share knowledge, and celebrate achievements together with 
                your learning community.
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="mt-12 text-center">
        <h2 className="text-2xl font-semibold text-gray-800 mb-4">Join the Learning Revolution</h2>
        <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
          Be part of a movement that values every voice and believes in the power of 
          community to transform education.
        </p>
        <button className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-8 rounded-lg transition-colors">
          Get Started
        </button>
      </div>
    </div>
  );
}
