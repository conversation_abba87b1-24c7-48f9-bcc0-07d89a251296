'use client';

import { useState } from 'react';
import { Settings, MessageSquare } from 'lucide-react';
import { useChatContext } from '@/contexts/ChatContext';
import SystemPromptEditor from './SystemPromptEditor';

export default function SystemPromptButton() {
  const [isEditorOpen, setIsEditorOpen] = useState(false);
  
  const {
    systemPrompts,
    activePromptId,
    addSystemPrompt,
    updateSystemPrompt,
    deleteSystemPrompt,
    setActivePrompt
  } = useChatContext();

  const handleSavePrompt = (prompt: { id?: string; name: string; content: string; isDefault: boolean }) => {
    if (prompt.id) {
      updateSystemPrompt(prompt.id, prompt);
    } else {
      addSystemPrompt(prompt);
    }
    if (prompt.isDefault) {
      const promptId = prompt.id || systemPrompts[systemPrompts.length - 1]?.id;
      if (promptId) setActivePrompt(promptId);
    }
  };

  return (
    <>
      <button
        onClick={() => setIsEditorOpen(true)}
        className="fixed left-4 bottom-20 p-3 bg-white rounded-full shadow-lg hover:bg-gray-100 transition-colors z-[60] flex items-center gap-2 text-sm font-medium"
        aria-label="System prompts"
      >
        <Settings className="w-5 h-5 text-gray-700" />
        <span>System Prompt</span>
      </button>
      
      <SystemPromptEditor 
        isOpen={isEditorOpen}
        onClose={() => setIsEditorOpen(false)}
        onSave={handleSavePrompt}
        prompts={systemPrompts}
        onSelect={setActivePrompt}
        onDelete={deleteSystemPrompt}
        activePromptId={activePromptId}
      />
    </>
  );
}
