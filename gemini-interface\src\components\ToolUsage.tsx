'use client';

import { useEffect, useState } from 'react';

type ToolCall = {
  id: string;
  toolName: string;
  timestamp: Date;
  status: 'pending' | 'success' | 'error';
  duration?: number;
  error?: string;
};

export default function ToolUsage() {
  const [toolCalls, setToolCalls] = useState<ToolCall[]>([]);
  const [isMinimized, setIsMinimized] = useState(false);

  // This would be replaced with actual tool call tracking
  // For now, we'll use a mock implementation
  useEffect(() => {
    // Simulate some tool calls for demo purposes
    const initialTools: ToolCall[] = [
      {
        id: '1',
        toolName: 'browser_navigate',
        timestamp: new Date(Date.now() - 5000),
        status: 'success',
        duration: 200
      },
      {
        id: '2',
        toolName: 'browser_click',
        timestamp: new Date(Date.now() - 3000),
        status: 'success',
        duration: 150
      },
      {
        id: '3',
        toolName: 'browser_type',
        timestamp: new Date(),
        status: 'pending',
      }
    ];
    
    setToolCalls(initialTools);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'bg-green-100 text-green-800';
      case 'error':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit' });
  };

  return (
    <div className="w-64 flex-shrink-0 border-r border-gray-200 bg-white flex flex-col">
      <div className="p-3 border-b border-gray-200">
        <h3 className="font-medium text-sm">Tool Usage</h3>
      </div>
      
      <div className="flex-1 overflow-y-auto">
        {toolCalls.length === 0 ? (
          <div className="p-4 text-center text-xs text-gray-500">
            No tool usage recorded yet
          </div>
        ) : (
          <ul className="divide-y divide-gray-100">
            {toolCalls.map((tool) => (
              <li key={tool.id} className="p-2 hover:bg-gray-50 transition-colors">
                <div className="flex items-start gap-2">
                  <span className={`inline-flex items-center px-1.5 py-0.5 rounded text-[10px] font-medium mt-0.5 ${getStatusColor(tool.status)}`}>
                    {tool.status.charAt(0).toUpperCase()}
                  </span>
                  <div className="flex-1 min-w-0">
                    <div className="text-xs font-medium truncate">{tool.toolName}</div>
                    <div className="text-[10px] text-gray-500">
                      {formatTime(tool.timestamp)}
                      {tool.duration && ` • ${tool.duration}ms`}
                    </div>
                    {tool.error && (
                      <div className="mt-0.5 text-[10px] text-red-600 bg-red-50 p-1 rounded truncate">
                        {tool.error}
                      </div>
                    )}
                  </div>
                </div>
              </li>
            ))}
          </ul>
        )}
      </div>
      
      <div className="p-2 border-t border-gray-200 text-[10px] text-gray-500 bg-gray-50">
        {toolCalls.length} tool calls
      </div>
    </div>
  );
}
